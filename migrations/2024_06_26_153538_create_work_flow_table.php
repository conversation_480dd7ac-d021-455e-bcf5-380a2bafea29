<?php

use Hyperf\Database\Migrations\Migration;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Schema\Schema;

class CreateWorkFlowTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('work_flow')) {
            Schema::create('work_flow', function (Blueprint $table) {
                $table->integerIncrements('id');
                $table->string('scene_type')->default('')->comment('场景类型');
                $table->integer('old_status_id')->default(0)->comment('旧状态，work_status表的id');
                $table->integer('new_status_id')->default(0)->comment('新状态，work_status表的id');
                $table->integer('create_user_id')->default(0)->comment('用户id');
                $table->tinyInteger('is_send_notice')->default(0)->comment('是否推送消息');
                $table->json('condition')->nullable()->comment('条件');
                $table->text('formula')->nullable()->comment('条件公式');
                $table->string('description')->default('')->comment('描述');
                $table->integer('level') ->default(0)->comment('优先级，数字越大优先级越高');
                #创建时间
                $table->dateTime('created_at')->nullable();
                #更新时间
                $table->dateTime('updated_at')->nullable();
                #删除时间
                $table->dateTime('deleted_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('work_flow');
    }
}

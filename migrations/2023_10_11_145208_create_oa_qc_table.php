<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateOaQcTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('oa_qc')) {
            Schema::create('oa_qc', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->smallInteger('type')->comment('类型');
                $table->string('order_no', 32)->comment('委外单号/采购订单号');
                $table->string('enter_no', 32)->comment('完工单号/入库单号');
                $table->string('prod_code', 32)->comment('料号');
                $table->string('prod_name', 64)->comment('名称');
                $table->string('prod_spec', 128)->comment('规格');
                $table->string('prod_loc', 8)->comment('仓库代码');
                $table->integer('num')->default(0)->comment('入库数量');
                $table->integer('examine_num')->default(0)->comment('报检数量');
                $table->tinyInteger('status')->default(0)->comment('检验结果状态');
                $table->tinyInteger('handle')->default(0)->comment('处理方案');
                $table->json('examine_option')->nullable()->comment('报查内容');
                $table->integer('examine_user')->default(0)->comment('检测人');
                $table->integer('appearance_examine_user')->default(0)->comment('外观测试员');
                $table->integer('function_examine_user')->default(0)->comment('功能测试员');
                $table->text('examine_remark')->nullable()->comment('检验结果状态');
                $table->text('result_remark')->nullable()->comment('检验结果状态');
                $table->dateTime('erp_date')->nullable()->comment('ERP数据时间');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('oa_qc');
    }
}

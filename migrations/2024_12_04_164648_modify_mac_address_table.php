<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class ModifyMacAddressTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('mac_address')) {
            Schema::table('mac_address', function (Blueprint $table) {
                if (!Schema::hasColumn('mac_address', 'origin_type')) {
                    $table->tinyInteger('origin_type')->default(1)->comment('类型,{1:天启2:客户}')->after('id');
                }
                if (!Schema::hasColumn('mac_address', 'used_relate_id')) {
                    $table->integer('used_relate_id')->default(0)->comment('关联的orderId')->after('used_type');
                }
                $table->index('mac_address');
                $table->index('mac_decimal');
                $table->index('used_no');
                $table->index(['used_type', 'used_relate_id']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('mac_address', function (Blueprint $table) {
            //
        });
    }
}

<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateModulesTemplateTable extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('modules_template')) {
            Schema::create('modules_template', function (Blueprint $table) {
                $table->integerIncrements('id');
                $table->string('project_type', 32)->comment('项目类型');
                $table->json('modules')->nullable()->comment('默认自带模块');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('modules_template');
    }
}

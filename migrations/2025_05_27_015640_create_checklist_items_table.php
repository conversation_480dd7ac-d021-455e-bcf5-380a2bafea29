<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateChecklistItemsTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('checklist_items', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('清单项主键，自增');
            $table->unsignedBigInteger('checklist_id')->comment('所属检查清单ID');
            $table->string('item_title', 255)->comment('检查项标题');
            $table->tinyInteger('status')->default(0)->comment('检查项状态（0=未完成；1=已完成）');
            $table->text('remarks')->nullable()->comment('备注，可填写对该清单项的补充说明');
            $table->integer('sort_order')->default(1)->comment('检查项在清单中的顺序');
            $table->json('custom_fields')->nullable()->comment('自定义字段值集合，以JSON格式存储');
            $table->integer('created_by')->comment('创建人（用户ID）');
            $table->dateTime('created_at')->useCurrent()->comment('创建时间');
            $table->dateTime('updated_at')->useCurrent()->useCurrentOnUpdate()->comment('更新时间');
            $table->dateTime('deleted_at')->nullable()->comment('软删除时间');

            $table->index('checklist_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('checklist_items');
    }
}

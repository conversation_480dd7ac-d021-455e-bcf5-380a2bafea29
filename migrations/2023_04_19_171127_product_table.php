<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class ProductTable extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product', function (Blueprint $table) {
            if(Schema::hasColumn('product', 'soft_handler_uid')){
                $table->integer('soft_handler_android_uid')->comment('Andorid软件负责人ID')
                    ->after('soft_handler_uid');
                $table->renameColumn('soft_handler_uid','soft_handler_linux_uid');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Schema::table('product', function (Blueprint $table) {
        //     $table->renameColumn('soft_handler_linux_uid','soft_handler_uid');
        //     $table->dropColumn('soft_handler_android_uid');
        // });
    }
}

<?php
/*
 * @Description: 添加状态字段到文化表
 * @Version: 1.0
 * @Autor: 张权江
 * @Date: 2025-03-20 16:52:33
 * @LastEditors: 张权江
 * @LastEditTime: 2025-03-20 16:54:54
 */

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class AddStatusToCultureTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('culture', function (Blueprint $table) {
            $table->tinyInteger('status')->default(1)->comment('状态：0-禁用，1-启用');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('culture', function (Blueprint $table) {
            $table->dropIndex(['status']);
            $table->dropColumn('status');
        });
    }
}

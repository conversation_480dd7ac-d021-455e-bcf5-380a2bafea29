<?php
/**
 * 测试事项复制功能的类型问题修复
 */

// 模拟测试数据
$testData = [
    "target_project_id" => 141,
    "copy_attachments" => true,
    "link_to_original" => true,
    "copy_description" => true,
    "copy_children" => true,
    "enable_sync" => false,
    "issues" => [
        [
            "id" => 22370,
            "subject" => "QC质检流程",
            "tracker_id" => 11,
            "status_id" => 2,
            "priority_id" => 3,
            "category_id" => 0,
            "fixed_version_id" => 214,
            "assigned_to_id" => 145,
            "assigned_to_ids" => [145],
            "start_date" => null,
            "due_date" => "2024-04-19",
            "class_id" => null,
            "watchers" => [],
            "custom_fields" => [],
            "parent_id" => null
        ],
        [
            "id" => 22373,
            "subject" => "质检列表",
            "tracker_id" => 11,
            "status_id" => 1,
            "priority_id" => 3,
            "fixed_version_id" => 214,
            "assigned_to_id" => 145,
            "assigned_to_ids" => [145],
            "due_date" => "2024-04-19",
            "class_id" => null,
            "watchers" => [],
            "parent_id" => null,
            "originalParentId" => 22370,
            "needsParentRelation" => true,
            "isChildIssue" => true
        ],
        [
            "id" => 22374,
            "subject" => "产品列表中可以显示产品上线信息",
            "tracker_id" => 11,
            "status_id" => 1,
            "priority_id" => 4,
            "fixed_version_id" => 214,
            "assigned_to_id" => 145,
            "assigned_to_ids" => [145],
            "due_date" => "2024-04-19",
            "class_id" => null,
            "watchers" => [],
            "parent_id" => null,
            "originalParentId" => 22373,
            "needsParentRelation" => true,
            "isChildIssue" => true
        ],
        [
            "id" => 22371,
            "subject" => "入库列表",
            "tracker_id" => 11,
            "status_id" => 6,
            "priority_id" => 3,
            "fixed_version_id" => 214,
            "assigned_to_id" => 145,
            "assigned_to_ids" => [145],
            "due_date" => "2024-04-19",
            "class_id" => null,
            "watchers" => [],
            "parent_id" => null,
            "originalParentId" => 22370,
            "needsParentRelation" => true,
            "isChildIssue" => true
        ]
    ],
    "parentChildMapping" => [
        "parentIssueId" => 22370,
        "childIssueIds" => [22373, 22374, 22371]
    ]
];

echo "测试数据结构验证:\n";
echo "目标项目ID: " . $testData['target_project_id'] . "\n";
echo "事项总数: " . count($testData['issues']) . "\n";

$parentIssues = array_filter($testData['issues'], function($issue) {
    return empty($issue['isChildIssue']);
});

$childIssues = array_filter($testData['issues'], function($issue) {
    return !empty($issue['isChildIssue']);
});

echo "父事项数量: " . count($parentIssues) . "\n";
echo "子事项数量: " . count($childIssues) . "\n";

echo "\n父子关系映射:\n";
foreach ($childIssues as $child) {
    echo "子事项 {$child['id']} ({$child['subject']}) -> 父事项 {$child['originalParentId']}\n";
}

echo "\n修复要点验证:\n";
echo "1. 子事项的 parent_id 应该为 null (前端已处理)\n";
echo "2. 子事项应该有 originalParentId 标记\n";
echo "3. 子事项应该有 needsParentRelation 标记\n";
echo "4. 子事项应该有 isChildIssue 标记\n";

foreach ($childIssues as $child) {
    $hasOriginalParentId = isset($child['originalParentId']);
    $hasNeedsParentRelation = isset($child['needsParentRelation']) && $child['needsParentRelation'];
    $hasIsChildIssue = isset($child['isChildIssue']) && $child['isChildIssue'];
    $parentIdIsNull = !isset($child['parent_id']) || $child['parent_id'] === null;
    
    echo "\n子事项 {$child['id']}:\n";
    echo "  - originalParentId: " . ($hasOriginalParentId ? "✓" : "✗") . "\n";
    echo "  - needsParentRelation: " . ($hasNeedsParentRelation ? "✓" : "✗") . "\n";
    echo "  - isChildIssue: " . ($hasIsChildIssue ? "✓" : "✗") . "\n";
    echo "  - parent_id is null: " . ($parentIdIsNull ? "✓" : "✗") . "\n";
}

echo "\n测试完成！\n";
?>

<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/14 下午7:58
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Redmine;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Project\AttachmentService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("Redmine接口-附件")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class AttachmentController extends \App\Controller\BaseController
{
    /**
     * @Inject
     * @var AttachmentService
     */
    protected $service;

    public function upload()
    {
        $file  = $this->request->file('file');
        $cId   = $this->request->input('container_id', '');
        $cType = $this->request->input('container_type', '');
        if ($file) {
            $result = $this->service->upload($file, $cId, $cType);
            return $this->response->success($result);
        }
        return $this->response->error(__('common.Missing_parameter'));
    }

}
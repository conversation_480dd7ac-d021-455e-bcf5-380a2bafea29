<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/21 下午5:29
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Redmine;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Redmine\SettingsService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("Redmine接口-设置")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class SettingsController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var SettingsService
     */
    protected $service;

    public function statusList()
    {
        $name = $this->request->input('name');
        $result = $this->service->statusList($name);
        return $this->response->success($result);
    }

    public function getSettingValue()
    {
        $name = $this->request->input('name');
        $type = $this->request->input('value_type', null);
        $result = $this->service->statusList($name, $type);
        return $this->response->success($result);
    }

    public function doEditByName()
    {
        $name = $this->request->input('name');
        $values = $this->request->input('values', null);
        $result = $this->service->doEditByName($name, $values);
        return $this->response->success($result);
    }
}
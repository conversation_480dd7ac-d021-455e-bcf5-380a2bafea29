<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 上午10:12
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Project\FlowService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("项目流程管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class FlowController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var FlowService
     */
    protected $service;

    public function createDefaultFlow()
    {
        $projectIds = $this->request->input('container_id');
        $flowType = $this->request->input('flow_type');
        $productType = $this->request->input('product_type');
        return $this->response->success($this->service->createDefaultFlow($projectIds, $flowType ?: 'project', $productType));
    }

    public function syncNodes()
    {
        $projectIds = $this->request->input('container_id');
        $flowType = $this->request->input('flow_type');
        return $this->response->success($this->service->syncNodes($projectIds, $flowType ?: 'project'));
    }

    public function addDefaultStationTask()
    {
        $flowId =  $this->request->input('flow_id') ?? 0;
        $nodes = $this->request->input('nodes') ?? [];
        $result = $this->service->addDefaultStationTask($nodes, $flowId);
        //$result = $this->service->nearTheDeadline();
        return $this->response->success($result);
    }
}
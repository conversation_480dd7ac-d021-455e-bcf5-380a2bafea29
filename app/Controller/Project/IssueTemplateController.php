<?php
/**
 * @Copyright T-chip Team.
 * @Date 2025/06/18 14:00
 * <AUTHOR>
 * @Description 事项模板控制器
 */

namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Project\IssueTemplateService;
use App\Request\IdsRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("事项模板管理")
 * @AutoController(prefix="/project/issue/template")
 */
class IssueTemplateController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var IssueTemplateService
     */
    protected $service;

    /**
     * @ControllerNameAnnotation("获取模板列表")
     * 获取事项模板列表
     * @return ResponseInterface
     */
    public function getList(): ResponseInterface
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->service->getTemplateList($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("获取模板详情")
     * 获取事项模板详情
     * @return ResponseInterface
     */
    public function getDetail(): ResponseInterface
    {
        $id = $this->request->input('id', 0);
        if (!$id) {
            return $this->response->error('模板ID不能为空');
        }
        
        $result = $this->service->getTemplateDetail($id);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("保存模板")
     * 新增/编辑事项模板
     * @return ResponseInterface
     */
    public function saveTemplate(): ResponseInterface
    {
        $id = $this->request->input('id', -1);
        // 确保ID为整数类型，-1表示新增，>0表示编辑
        $id = (int)$id;
        $templateData = $this->request->input('template_data', []);
        $checklistItems = $this->request->input('checklist_items', []);
        
        if (empty($templateData)) {
            return $this->response->error('模板数据不能为空');
        }
        
        // 验证必要字段
        $requiredFields = ['project_id', 'tracker_id', 'name'];
        foreach ($requiredFields as $field) {
            if (empty($templateData[$field])) {
                return $this->response->error("字段 {$field} 不能为空");
            }
        }
        
        $result = $this->service->saveTemplate($id, $templateData, $checklistItems);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("删除模板")
     * 删除事项模板
     * @return ResponseInterface
     */
    public function delete(IdsRequest $request): ResponseInterface
    {
        $validated = $request->validated();
        $result = $this->service->deleteTemplates($validated['ids']);
        
        if ($result) {
            return $this->response->success([], '删除成功');
        } else {
            return $this->response->error('删除失败');
        }
    }

    /**
     * @ControllerNameAnnotation("更新模板状态")
     * 更新模板启用/禁用状态
     * @return ResponseInterface
     */
    public function updateStatus(): ResponseInterface
    {
        $id = $this->request->input('id', 0);
        $isActive = $this->request->input('is_active', 1);
        
        if (!$id) {
            return $this->response->error('模板ID不能为空');
        }
        
        $result = $this->service->updateTemplateStatus($id, $isActive);
        
        if ($result) {
            return $this->response->success([], '状态更新成功');
        } else {
            return $this->response->error('状态更新失败');
        }
    }

    /**
     * @ControllerNameAnnotation("根据项目和事项类型获取模板")
     * 获取指定项目和事项类型的模板列表
     * @return ResponseInterface
     */
    public function getByProjectAndTracker(): ResponseInterface
    {
        $projectId = $this->request->input('project_id', 0);
        $trackerId = $this->request->input('tracker_id', 0);
        
        if (!$projectId || !$trackerId) {
            return $this->response->error('项目ID和事项类型ID不能为空');
        }
        
        $result = $this->service->getTemplatesByProjectAndTracker($projectId, $trackerId);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("从模板获取检查项")
     * 根据模板ID获取检查清单项
     * @return ResponseInterface
     */
    public function getChecklistItems(): ResponseInterface
    {
        $templateId = $this->request->input('template_id', 0);
        $issueId = $this->request->input('issue_id', null);
        
        if (!$templateId) {
            return $this->response->error('模板ID不能为空');
        }
        
        $result = $this->service->getChecklistItemsFromTemplate($templateId, $issueId);
        return $this->response->success($result);
    }
} 
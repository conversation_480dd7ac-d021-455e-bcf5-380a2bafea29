<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/5/22 上午9:59
 * <AUTHOR>
 * @Description
 */


namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Project\CustomFieldsProjectsService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use App\Middleware\MenuMiddleware;

/**
 * @ControllerNameAnnotation("项目自定义属性")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class CustomFieldsProjectsController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var CustomFieldsProjectsService
     */
    protected $service;

    public function getProjectFieldList()
    {
        $projectId = $this->request->input('project_id');
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getProjectFieldList($projectId, $filter, $op, $sort, $order);
        return $this->response->success($result);
    }
}
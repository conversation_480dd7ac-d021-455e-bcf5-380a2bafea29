<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/29 下午5:04
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Request\Project\ProjectsTypeRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use App\Core\Services\Project\ModulesTemplateService;

/**
 * @ControllerNameAnnotation("项目模块")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class ModulesTemplateController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var ModulesTemplateService
     */
    protected $service;


    public function getList()
    {
        return parent::getList(); // TODO: Change the autogenerated stub
    }

    public function getTypeDefaultModules()
    {
        $validated = make(ProjectsTypeRequest::class)->validated();
        $result = $this->service->getTypeDefaultModules($validated['project_type']);
        return $this->response->success($result);
    }
}
<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 上午10:12
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Project\JournalService;
use App\Core\Services\Redmine\AttachmentService;
use App\Request\Project\Issue\DoMultiCreateRequest;
use App\Request\Project\NewIssueStatusRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("任务日志")
 * @AutoController(prefix="/project/journal/index")
 * @Middleware(AuthMiddleware::class)
 */
class JournalController extends \App\Controller\BaseController
{

    /**
     * @Inject()
     * @var JournalService
     */
    protected $service;
}
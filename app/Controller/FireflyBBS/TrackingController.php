<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\FireflyBBS;

use App\Annotation\ControllerNameAnnotation;
use App\Constants\FollowUpSource;
use App\Core\Services\FireflyBBS\TrackingService;
use App\Model\TchipBi\OaBbsFollowModel;
use App\Model\TchipBi\UserThirdModel;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Annotation\WorkWxTokenAnnotation;

use App\Controller\BaseController;
use App\Core\Services\FireflyBBS\Tracking;
use App\Core\Services\WorkWx\WorkWxMessageService;
use App\Model\TchipBi\UserModel;
use Hyperf\Guzzle\ClientFactory;
use Psr\Http\Message\ResponseInterface;
use Qbhy\HyperfAuth\AuthManager;
use Symfony\Component\String\UnicodeString;

/**
 * @ControllerNameAnnotation("fireflyBBs 帖子跟进")
 * @Controller()
 */
class TrackingController extends BaseController
{

    /**
     * @Inject()
     * @var AuthManager
     */
    protected $auth;

    /**
     * @var \Hyperf\Guzzle\ClientFactory
     */
    private $clientFactory;
    /**
     * @Inject()
     * @var WorkWxMessageService
     */
    protected $workWxMessageService;
    /**
     * @Inject()
     * @var TrackingService
     */
    protected $fireflyThreadService;

    /**
     * 数据源列表
     * @RequestMapping(path="/fireflybbs/tracking/source")
     * @return ResponseInterface
     */
    public function source()
    {
        return $this->response->success(FollowUpSource::info());
    }

    /**
     * firefly平台帖子处理人 列表
     * @RequestMapping(path="/fireflybbs/tracking/users")
     * @return ResponseInterface
     */
    public function users()
    {
        return $this->response->success($this->fireflyThreadService->processor());
    }

    /**
     * firefly平台帖子回复处理
     * @RequestMapping(path="/fireflybbs/tracking/reply")
     * @return ResponseInterface
     */
    public function reply()
    {
        $params = $this->request->all();
        validate($params,
            [
                'source'     => 'int|required|min:1',
                'bbs_uid'    => 'int|required|min:1',
                'tid'        => 'int|required|min:1',
                'content'    => 'string',
                'reply_time' => 'string',
                'status'     => 'int|min:0|max:2',
            ]
        );
        //var_dump($params);
        $tid    = $params['tid'];
        $bbsUid = $params['bbs_uid'];
        $source = $params['source'];

        $res = [];
        if (isset($params['content']) && isset($params['reply_time'])) {
            $content    = $params['content'];
            $reply_time = date('Y-m-d H:i', strtotime($params['reply_time']));

            $data      = OaBbsFollowModel::where(['tid' => $tid, 'source' => $source])->first();
            $updateArr = ['last_reply' => $content, 'last_reply_time' => $reply_time];
            //更新 跟进信息
            OaBbsFollowModel::where(['tid' => $tid, 'source' => $source])->update($updateArr);

            $biUid = $data['follower'];
            //推送消息
            if ($biUid && $bbsUid && $data && $data['follower']) {

                $where          = ['platform' => FollowUpSource::getPlatformBySource($source), 'user_id' => $biUid, 'third_user_id' => $bbsUid];
                $myThirdAccount = UserThirdModel::where($where)->first();
                //不是已绑定的账号，则推送微信消息
                if (!$myThirdAccount) {
                    $to_user = $data['follower'];
                    $biUser  = UserModel::where('id', $to_user)->first();
                    if ($biUser) {
                        $source_txt = FollowUpSource::text($source);
                        $link       = $this->threadLink($tid, $source);
//                         $tpl        = <<<EOT
// Hi {$biUser->name}，
// 数字天启提醒您：
// 收到来至 {$source_txt} 的跟进信息。
// 标   题：{$data["subject"]}
// 帖子状态：{$data["status_txt"]}
// 时   间：{$reply_time}
// 回复内容：
// {$content}
//
// <a href="{$link}">点击查看详情</a>
// EOT;

                        // $res = $this->workWxMessageService->sendMarkdown($tpl, $biUser->workwx_userid, '', '');
                        $templateCard = [
                            'msgtype'       => 'template_card',
                            "template_card" => [
                                "card_type"   => "text_notice",
                                "source"      => [
                                    "desc"       => $source_txt,
                                    "icon_url"   => "https://dev.t-firefly.com/template/image/logo2023.png",
                                    "desc_color" => 1
                                ],
                                "main_title"  => [
                                    "title" => $data["subject"],
                                    "desc"  => $reply_time . ' 您负责的帖子有新的回复'
                                ],
                                "quote_area"  => [
                                    "quote_text" => $content
                                ],
                                "jump_list"   => [
                                    [
                                        "type"  => 1,
                                        "title" => "查看详情",
                                        "url"   => $link
                                    ],
                                ],
                                "card_action" => [
                                    "type" => 1,
                                    "url"  => $link,
                                ]
                            ],
                        ];
                        $res          = $this->workWxMessageService->templateCard($templateCard, $biUser->workwx_userid, '', '');
                    }
                }

            }
        }
        //
        if (isset($params['status'])) {
            //更新 跟进信息
            OaBbsFollowModel::where(['tid' => $tid, 'source' => $source])->update(['status' => intval($params['status'])]);
        }

        return $this->response->success($res);
    }


    private function threadLink($tid, $source)
    {
        return FollowUpSource::info($source)['host'] . '/forum.php?mod=viewthread&tid=' . $tid;
    }

    /**
     * firefly账号
     * @RequestMapping(path="/fireflybbs/account/search")
     * @return ResponseInterface
     */
    public function accountSearch()
    {

        //var_dump(implode(',', FollowUpSource::platform()));
        $params = $this->request->all();
        validate($params,
            [
                'platform' => 'string|required|in:' . implode(',', FollowUpSource::platform()),
            ]
        );
        $platform = $params['platform'];
        $host     = FollowUpSource::getInfoByPlatform($platform)['host'];
        //$platform = FollowUpSource::info($params['source'])['platform'];


        //$bbsUid = $this->request->input('bbs_uid');
        $username = $this->request->input('username');
        //$password = $this->request->input('password');


        $clientFactory = make(ClientFactory::class)->create([
            'base_uri' => $host,
            'timeout'  => 5
        ]);
        $response      = $clientFactory->request('post', '/plugin.php?id=tchip_bi_topic_tracking',
            [
                'form_params' => ['action'   => 'searchUsername',
                                  //'bbs_uid' => $bbsUid
                                  'username' => $username,
                                  //'password' => $password
                ],
            ]);
        $responseArr   = json_decode($response->getBody()->getContents(), true);
        if ($responseArr && isset($responseArr['data'])) {
            return $this->response->success($responseArr['data']);
        }
        return $this->response->error(__('Data request exception'));
    }

    /**
     * 标记自用的firefly账号
     * @RequestMapping(path="/fireflybbs/tracking/markAccount")
     * @return ResponseInterface
     */
    public function markAccount()
    {
        $params = $this->request->all();
        validate($params,
            [
                'platform' => 'string|required|in:' . implode(',', FollowUpSource::platform()),
                'uid'      => 'int|required|min:1',
                'username' => 'string|required',
            ]
        );
        //$host     = FollowUpSource::getInfoByPlatform($params['platform'])['host'];
        $platform = $params['platform'];


        $bbsUid   = $params['uid'];
        $username = $params['username'];
        $biUid    = $this->auth->user()->getId();
        if ($bbsUid && $biUid) {
            $where = ['platform' => $platform, 'user_id' => $biUid, 'third_user_id' => $bbsUid];
            $mark  = UserThirdModel::where($where)->first();
            if (!$mark) {
                $where['remark'] = json_encode(['username' => $username ?? '']) ?? '';
                UserThirdModel::create($where);
                return $this->response->success([]);
            } else {
                return $this->response->error(__('common.Data_is_exist'));
            }
        }

        return $this->response->error(__('Data request exception'));
    }

    /**
     * 标记自用的firefly账号 列表
     * @RequestMapping(path="/fireflybbs/tracking/markAccount/list")
     * @return ResponseInterface
     */
    public function markAccountList()
    {
        $params = $this->request->all();
        validate($params,
            [
                'platform' => 'string|in:' . implode(',', FollowUpSource::platform()),
            ]
        );
        //$host     = FollowUpSource::info($params['source'])($params['source'])['host'];
        if (isset($params['platform'])) {
            $platform = $params['platform'];
        } else {
            $platform = FollowUpSource::platform();
        }

        $biUid = $this->auth->user()->getId();

        //$where    = ['platform' => $platform, 'user_id' => $biUid];
        $where    = ['user_id' => $biUid];
        $markList = UserThirdModel::where($where)->whereIn('platform', $platform)->select(['id', 'user_id', 'platform', 'third_user_id', 'remark'])->get();
        $markList = $markList ? $markList->toArray() : [];
        foreach ($markList as $key => $value) {
            $arr                            = $value['remark'] ? json_decode($value['remark'], true) : [];
            $markList[$key]['username']     = $arr['username'] ?? '';
            $markList[$key]['platform_txt'] = FollowUpSource::getInfoByPlatform($value['platform'])['text'] ?? '';
        }
        $markListTotal = UserThirdModel::where($where)->whereIn('platform', $platform)->count(['id']);
        return $this->response->success(['data' => $markList, 'total' => $markListTotal ?? 0]);
    }

    /**
     * 取消标记自用的firefly账号
     * @RequestMapping(path="/fireflybbs/tracking/unmarkAccount")
     * @return ResponseInterface
     */
    public function unmarkAccount()
    {
        $params = $this->request->all();
        validate($params,
            [
                'platform' => 'string|required|in:' . implode(',', FollowUpSource::platform()),
                'bbsUid'   => 'int|required',
            ]
        );
        //$host     = FollowUpSource::info($params['source'])($params['source'])['host'];
        //$platform = FollowUpSource::info($params['source'])($params['source'])['platform'];
        $platform = $params['platform'];
        $bbsUid   = $params['bbsUid'];
        $biUid    = $this->auth->user()->getId();
        $res      = 0;
        if ($bbsUid && $biUid) {
            $where = ['platform' => $platform, 'user_id' => $biUid, 'third_user_id' => $bbsUid];
            $res   = UserThirdModel::where($where)->delete();
        }
        return $this->response->success(['result' => $res]);
    }

    /**
     * @RequestMapping(path="/fireflybbs/tracking/list")
     * @return ResponseInterface
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function trackingList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        if (isset($filter['source']) && intval($filter['source']) === 0) {
            unset($filter['source']);
        }
        $result = $this->fireflyThreadService->getList($filter, $op, $sort, $order, (int)$limit, $search, $searchFields);
        //return $this->response->success($result);
        $result = $result ? $result->toArray() : [];
        if ($result && isset($result['data'])) {
            $data     = $result['data'] ?? [];
            $userList = UserModel::query()->whereIn('id', array_column($data, 'follower'))->select(['id', 'name'])->get()->toArray();
            $userList = array_column($userList, null, 'id');
            foreach ($data as $key => $val) {
                //var_dump($val);
                $data[$key]['link']         = $this->threadLink($val['tid'], $val['source']);
                $data[$key]['follower_txt'] = $userList[$val['follower']]['name'] ?? '';
                //$data[$key]['status_txt']   = $val['status'] == 0 ? '跟进中' : ($val['status'] == 1 ? '已解决' : '已关闭');
                //$data[$key]['source_txt']   = FollowUpSource::text($val['source']);
            }
            $result['data'] = $data;
            return $this->response->success($result);
        }
        return $this->response->error('data null');
    }

    /**
     * @RequestMapping(path="/fireflybbs/tracking/set")
     * @return ResponseInterface
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function setTracking()
    {
        $params = $this->request->all();
        validate($params,
            [
                'source'       => 'int|min:1|required', //
                'tid'          => 'int|min:1|required', //
                'processor_id' => 'int|min:0|required',
                'processor'    => 'string|required',
            ]
        );
        $source       = $params['source'];
        $tid          = $params['tid'];
        $processor_id = $params['processor_id'];
        $processor    = $params['processor'];

        $trackingLog = OaBbsFollowModel::query()->where('tid', $tid)->where('source', $source)->first();
        //$trackingLog = DB::fetch_first("SELECT * FROM " . DB::table('topic_tracking') . " where tid=$tid limit 1");
//var_dump($params);
//var_dump($trackingLog);
        if (!$processor_id) {
            OaBbsFollowModel::query()->where('tid', $tid)->where('source', $source)->delete();
            return $this->response->success(['del_id' => $trackingLog['id']]);
            //DB::delete('topic_tracking', DB::field('tid', $tid));
            //msg(0, '', ['del_id' => $trackingLog['id']]);
        }
        if ($trackingLog) {
            //更新 帖子跟进负责人
            OaBbsFollowModel::query()->where('tid', $tid)->where('source', $source)->update(['follower' => $processor_id]);
            return $this->response->success(['tid' => $tid]);
            //DB::update('topic_tracking', ['processor_id' => $processor_id, 'processor' => $processor], ['tid' => $tid]);
            //msg(0, '', ['tid' => $tid]);

        } else {

            $params = $this->request->all();
            validate($params,
                [
                    'subject'             => 'string|required',
                    'authorid'            => 'int|min:1|required',
                    'author'              => 'string|required',
                    'final_reply_content' => 'string',
                    'last_reply_time'     => 'string',
                ]
            );

            $subject             = $params['subject'] ?? '';
            $authorid            = $params['authorid'] ?? '';
            $author              = $params['author'] ?? '';
            $final_reply_content = $params['final_reply_content'] ?? '';
            $last_reply_time     = $params['last_reply_time'] ?? date('Y-m-d H:i:s');
            $status              = $params['status'] ?? 0;
            $avatar              = $params['avatar'] ?? '';


            //添加 帖子跟进负责人
            $insertData = [
                'source'          => $source,
                'subject'         => $subject,
                'tid'             => $tid,
                'authorid'        => $authorid,
                'author'          => $author,
                'follower'        => $processor_id,
                //'processor'       => $processor,
                'last_reply'      => $final_reply_content,
                'last_reply_time' => $last_reply_time ? $last_reply_time : null,
                'status'          => $status,
                'avatar'          => $avatar,
            ];
            $insert_id  = OaBbsFollowModel::query()->insertGetId($insertData);
            return $this->response->success(['id' => $insert_id]);
        }


    }

    /**
     * @RequestMapping(path="/fireflybbs/tracking/getFollower")
     * @return ResponseInterface
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getFollower()
    {
        $params = $this->request->all();
        validate($params,
            [
                'tid' => 'int|min:1|required', //
            ]
        );
        $tid = $params['tid'];

        $trackingLog = OaBbsFollowModel::query()->where('tid', $tid)->first();
        if (!$trackingLog) {
            return $this->response->success(['follower' => 0]);
        }
        return $this->response->success(['follower' => $trackingLog['follower']]);
    }


}
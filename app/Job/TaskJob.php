<?php

declare(strict_types=1);

namespace App\Job;

use App\Core\Utils\Log;
use App\Exception\AppException;
use App\Model\TchipBi\CrontabModel;
use Hyperf\Database\Exception\QueryException;
use Hyperf\Di\Annotation\Inject;
use App\Annotation\WorkWxTokenAnnotation;
use App\Core\Services\WorkWx\WorkWxMessageService;
use App\Core\Services\Product\ProductProgressService;
use App\Mail\Redmine\ProductEditMail;
use App\Model\Redmine\ProductMemberModel;
use App\Model\Redmine\ProductModel;
use App\Model\TchipBi\UserModel;
use Hyperf\AsyncQueue\Job;
use HyperfExt\Mail\Mail;
use App\Constants\StatusCode;

/**
 * 导步队列执行task任务
 */
class TaskJob extends Job
{
    public $params;

    /**
     * 任务执行失败后的重试次数，即最大执行次数为 $maxAttempts+1 次
     */
    protected $maxAttempts = 2;

    /**
     * @param $params [product_id, product_progress_id]
     */
    public function __construct($params)
    {
        // 这里最好是普通数据，不要使用携带 IO 的对象，比如 PDO 对象
        $this->params = $params;
    }

    public function handle()
    {
        // 根据参数处理具体逻辑
        // 通过具体参数获取模型等
        // 这里的逻辑会在 ConsumerProcess 进程中执行
        $cron = \App\Model\TchipBi\CrontabModel::query()->find($this->params['id']);
        if ($cron) {
            $crond = $cron->toArray();
            if ($crond['class'] && class_exists($crond['class'])) {
                if (method_exists($crond['class'], $crond['method'])) {
                    try {
                        $class = $crond['class'];
                        $method = $crond['method'];
                        $services = make($class);
                        $cron->update(['run_status' => 2]);

                        $services->$method();
                        $cron->update(['executetime' => date('Y-m-d H:i:s'), 'run_status' => 1]);
                    } catch (AppException $e) {
                        $cron->update(['run_status' => 3]);
                        throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
                    } catch (QueryException $e) {
                        $cron->update(['run_status' => 3]);
                        throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
                    } catch (\Exception $e) {
                        $cron->update(['run_status' => 3]);
                        throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
                    } catch ( \Qbhy\HyperfAuth\Exception\AuthException $e) {
                        $cron->update(['run_status' => 3]);
                        return $e->getMessage();
                    }
                }
            }
            return true;
        }
        return false;
    }
}
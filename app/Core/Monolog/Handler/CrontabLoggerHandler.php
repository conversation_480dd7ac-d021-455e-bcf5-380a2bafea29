<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date      2023/4/15 下午3:37
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Monolog\Handler;

use Hyperf\Context\Context;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Logger;

/**
 *
 */
class CrontabLoggerHandler extends RotatingFileHandler
{
    const CrontabTaskName = 'CrontabLog';

    /**
     * @param string   $filename
     * @param int      $maxFiles       The maximal amount of files to keep (0 means unlimited)
     * @param int|null $filePermission Optional file permissions (default (0644) are only for owner read/write)
     * @param bool     $useLocking     Try to lock log file before doing any writes
     */
    public function __construct(string $filename, int $maxFiles = 0, $level = Logger::DEBUG, bool $bubble = true, ?int $filePermission = null, bool $useLocking = false)
    {
        $taskName=Context::get(CrontabLoggerHandler::CrontabTaskName,null);
        !$taskName && $taskName='task';
        $filename=$filename.$taskName.'/'.date('Y-m').'/.log';
        parent::__construct($filename, $maxFiles, $level = Logger::DEBUG, $bubble, $filePermission, $useLocking);
        //$this->filenameFormat = '{filename}-{date}';
        //static::FILE_PER_DAY
        $this->setFilenameFormat('{date}' ,static::FILE_PER_DAY);
    }

}
<?php

namespace App\Core\Services\TchipOa;

use App\Constants\StatusCode;
use App\Exception\AppException;
use App\Model\Model;
use App\Model\TchipBi\AttachmentModel;
use App\Model\TchipBi\OaCompanyModel;
use App\Model\TchipBi\OaUserFilesModel;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Qbhy\HyperfAuth\AuthManager;

class OaFilesService extends OaBaseService
{
    /**
     * @Inject
     * @var OaUserFilesModel
     */
    public $model;

    /**
     * @Inject
     * @var AuthManager
     */
    protected $auth;

    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        /**@var $query OaUserFilesModel */
        $ageKey = ['age' => 'birthday', 'work_age' => 'entry_date'];
        foreach ($ageKey as $key => $field) {
            if (!empty($filter[$key]) && $filter[$key] > 0) {
                $y = date('Y') - $filter[$key];
                $sYear = $y . '-01-01';
                $nowYear = $y. '-12-31';
                $filter[$field] = $sYear.' - '.$nowYear;
                $op[$field] = 'DATETIME';
                unset($filter[$key]);
            }
        }
        $isEntry = null;
        if (!empty($filter['is_entry'])) {
            $isEntry = 1;
            unset($filter['is_entry']);
        }
        $isBirthday = null;
        if (!empty($filter['is_birthday'])) {
            $isBirthday = 1;
            unset($filter['is_birthday']);
        }

        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        $selectRaw = "*, IF(DATE_FORMAT(now(), '%m') = DATE_FORMAT(birthday, '%m'), 1, 0) as is_birthday, IF(DATE_FORMAT(now(), '%m') = DATE_FORMAT(entry_date, '%m'), 1, 0) as is_entry, DATE_FORMAT(now(), '%m') as m1, DATE_FORMAT(birthday, '%m')as m2";
        /* @var Model $query*/
        $paginate = $query->selectRaw($selectRaw)
            ->with(['company:id,name', 'departmentName:id,name', 'atWorkCompany:id,name']);
        if ($isEntry) {
            $paginate = $paginate->having('is_entry', $isEntry);
        }
        if ($isBirthday) {
            $paginate = $paginate->having('is_birthday', $isBirthday);
        }
        $paginate = $paginate->orderBy($sort, $order)->paginate($limit)->toArray();

        //获取附件的信息
        $attachmentArr = array_merge(array_column($paginate['data'], 'diploma_attachment_id'),
            array_column($paginate['data'], 'degree_attachment_id'),
            array_column($paginate['data'], 'identity_attachment_id'),
            array_column($paginate['data'], 'resume_attachment_id'),
        );
        $attachmentArr = array_unique(array_filter($attachmentArr));
        if ($attachmentArr) {
            $attachmentInfoArr = AttachmentModel::query()->whereIn('id', $attachmentArr)->get()->toArray();
            $attachmentInfoArr = array_column($attachmentInfoArr, null, 'id');
            foreach ($attachmentInfoArr as &$value) {
                $value['content_type'] = $value['mimetype'];
                $value['url'] = $value['full_url'];
            }
        }
        foreach ($paginate['data'] as &$item) {
            //返回二维数组给前端展示
            $item['attachment'] = [
                'diploma' => empty($attachmentInfoArr[$item['diploma_attachment_id']])?[]:[$attachmentInfoArr[$item['diploma_attachment_id']]],
                'degree' => empty($attachmentInfoArr[$item['degree_attachment_id']])?[]:[$attachmentInfoArr[$item['degree_attachment_id']]],
                'identity' => empty($attachmentInfoArr[$item['identity_attachment_id']])?[]:[$attachmentInfoArr[$item['identity_attachment_id']]],
                'resume' => empty($attachmentInfoArr[$item['resume_attachment_id']])?[]:[$attachmentInfoArr[$item['resume_attachment_id']]],
            ];
            $tempCountArr = [
                $item['diploma_attachment_id'],
                $item['degree_attachment_id'],
                $item['identity_attachment_id'],
                $item['resume_attachment_id'],
            ];
            //统计上传数
            $item['attach_uploaded_count'] = count(array_filter($tempCountArr));
            $item['attach_total_count'] = count($tempCountArr);
        }

        // 生日排序和工龄排序
        if ($sort == 'birthday' || $sort == 'age' || $sort == 'entry_date') {
            $orderField = ($sort == 'birthday' || $sort == 'age') ? 'age' : 'work_age';
            $key = array_column(array_values($paginate['data']), $orderField);
            array_multisort($key, ($order == 'DESC' ? SORT_DESC : SORT_ASC), $paginate['data']);
        }
        return $paginate;
    }

    public function doEdit(int $id, array $values)
    {
        // 创建数据需要初始化日期数据
        $this->filtterField($values);
        if ($id) {
            $id = str_pad((string)((int) $id), 6, "0", STR_PAD_LEFT);
            $row = $this->model::query()->where('id', $id)->first();
            if (!$row) {
                throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
            }
            $ip = explode(';', $row->ip);
            $ip[] = get_client_ip();
            if (count($ip) > 2) {
                array_splice($ip, 0, 1);
            }
            if (isset($values['company_id'])) {
                $values['company_id'] = (int) $values['company_id'];
            }
            if (isset($values['at_work_company_id'])) {
                $values['at_work_company_id'] = (int) $values['at_work_company_id'];
            }
            if (isset($values['part_id'])) {
                $values['part_id'] = (int) $values['part_id'];
            }
            $values['ip'] = implode(';', $ip);

            //2025-03-24 增加操作人记录
            $values['operator'] = $this->auth->user()->getId();
            $result = $row->update($values);
        } else {
            Db::beginTransaction();
            try {
                $values['ip'] = get_client_ip();
                // $last = $this->model::withTrashed()->orderBy('id', 'DESC')->first()->toArray();
                // $values['id'] = $last['id'] + 1;
                // $values['id'] = str_pad((string)$values['id'], 6, "0", STR_PAD_LEFT);

                // 查询现有的最后ID
                $lastId = $this->model::query()->orderBy('id', 'DESC')->value('id');
                $values['id'] = $lastId + 1;
                $values['id'] = str_pad((string)$values['id'], 6, "0", STR_PAD_LEFT);
                // 查询是否有已删除的最后一个ID
                $existDelete = $this->model::withTrashed()->where('id', $values['id'])->first();
                if ($existDelete) {
                    $existDelete->forceDelete();
                }

                // 初始化公司参数数据
                if (!isset($values['company_id']) || $values['company_id'] === null || $values['company_id'] === '') {
                    $values['company_id'] = 0;
                }
                // 初始化部门参数数据
                if (!isset($values['part_id']) || $values['part_id'] === null || $values['part_id'] === '') {
                    $values['part_id'] = 0;
                }
                //2025-03-24 增加操作人记录
                $values['operator'] = $this->auth->user()->getId();
                $result = $this->model::query()->create($values);
                Db::commit();
            } catch (\Throwable $e) {
                Db::rollBack();
                throw new AppException(StatusCode::ERR_EXCEPTION, $e->getMessage());
            }
        }
        return $result;
    }

    /**
     * 获取本月生日员工
     * @return \Hyperf\Utils\HigherOrderTapProxy|int|mixed|\Tightenco\Collect\Support\HigherOrderTapProxy|void
     */
    public function birthdayCount()
    {
        $selectRaw = "count(id) as count, IF(DATE_FORMAT(now(), '%m') = DATE_FORMAT(birthday, '%m'), 1, 0) as is_birthday";
        $countRow = $this->model->selectRaw($selectRaw)->having('is_birthday', 1)->where('incumbency', '在职')
        ->groupBy('is_birthday')->first();
        return $countRow->count ?? 0;
    }

    /**
     * 获取本月工龄奖员工
     * @return \Hyperf\Utils\HigherOrderTapProxy|int|mixed|\Tightenco\Collect\Support\HigherOrderTapProxy|void
     */
    public function entryCount()
    {
        $selectRaw = "count(id) as count, IF(((DATE_FORMAT(now(), '%m') * 30) + DATE_FORMAT(now(), '%d')) -
          (DATE_FORMAT(entry_date, '%m') * 30 + DATE_FORMAT(entry_date, '%d')) >= 0 and
          ((DATE_FORMAT(now(), '%m') * 30) + DATE_FORMAT(now(), '%d')) -
          (DATE_FORMAT(entry_date, '%m') * 30 + DATE_FORMAT(entry_date, '%d')) <= 15, 1, 0) as is_entry";
        $countRow = $this->model->selectRaw($selectRaw)->where('incumbency', '在职')->groupBy('is_entry')->having('is_entry', 1)->first();
        return $countRow->count ?? 0;
    }

    /**
     * 获取在职员工人数
     * @return int
     */
    public function incumbencyCount()
    {
        $selfCompany = OaCompanyModel::query()->whereIn('name', ['中山天启', '中山萤火'])->pluck('id')->toArray();
        return $this->model::query()->where(function ($query) use ($selfCompany) {
            if ($selfCompany) {
                $query->whereIn('company_id', $selfCompany);
            }
        })->where('incumbency', '在职')->count();
    }

    protected function filtterField(array &$values)
    {
        $timdates = ['birthday', 'graduate_date', 'separation_date', 'entry_date', 'positive_date', 'contract_term_end', 'contract_term'];
        $valueKeys = array_keys($values);
        foreach ($timdates as $timdate) {
            if (in_array($timdate, $valueKeys) && ($values[$timdate] == null || $values[$timdate] == '')) {
                $values[$timdate] = '0000-00-00';
            }
        }
    }

    /**
     * 同步原公司company_id到所在公司at_work_company_id
     * @return void
     */
    public function syncAtWorkCompanyId()
    {
        Db::transaction(function() {
            $this->model::query()
                ->whereNotNull('company_id')
                ->where('at_work_company_id', '=', null)
                ->update(['at_work_company_id' => \Hyperf\DbConnection\Db::raw('company_id')])
            ;
            $this->model::query()
                ->whereNotNull('company_id')
                ->whereIn('at_work_company_id', [1012])
                ->update(['company_id' => 1001]); //中山萤火 company_id全部更新为中山天启
        });
    }
}
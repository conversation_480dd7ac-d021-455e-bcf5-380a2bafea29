<?php

namespace App\Core\Services\TchipOa;

use App\Model\TchipBi\OaProductBorrowJournalModel;
use Hyperf\Di\Annotation\Inject;
use Qbhy\HyperfAuth\AuthManager;

class OaProductBorrowJournalService extends OaBaseService
{
    /**
     * @Inject()
     * @var OaProductBorrowJournalModel
     */
    protected $model;

    /**
     * @Inject()
     * @var AuthManager
     */
    protected $auth;

    public function diffSave($bId, $oldValue, $newValue, $type, $uid = null)
    {
        $uid = $uid ?? $this->auth->user()->getId();
        // 1.先抽取同KEY的数据
        // $alike = array_intersect_key($oldValue, $newValue);
        $alike = array_keys(array_intersect_key($oldValue, $newValue));
        foreach ($oldValue as $oKey => $oVal) {
            if (!in_array($oKey, $alike)) {
                unset($oldValue[$oKey]);
            }
        }
        foreach ($newValue as $nKey => $nVal) {
            if (!in_array($nKey, $alike)) {
                unset($newValue[$nKey]);
            }
        }
        // 2.对比数据是否有不一样
        $diffOldValue = [];
        $diffNewValue = [];
        foreach ($newValue as $key => $value) {
            if (isset($oldValue[$key]) && $value != $oldValue[$key]) {
                $diffOldValue[$key] = $oldValue[$key];
                $diffNewValue[$key] = $value;
            }
        }
        // $diffOldValue = array_diff_assoc($newValue, $oldValue);
        // $diffNewValue = array_diff_assoc($oldValue, $newValue);
        if (($diffOldValue && count($diffOldValue) > 0) && $diffNewValue && count($diffNewValue) > 0) {
            $saveJou = [
                'type' => $type,
                'user_id' => $uid,
                'b_id' => $bId,
                'old_value' => $diffOldValue,
                'value' => $diffNewValue,
            ];
            $this->doEdit(0, $saveJou);
        }
    }

}
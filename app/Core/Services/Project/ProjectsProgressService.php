<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/10/26 下午5:24
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Project;

use App\Constants\DataBaseCode;
use App\Constants\ProductCode;
use App\Constants\ProjectCode;
use App\Constants\StatusCode;
use App\Core\Services\Project\ProgressService;
use App\Core\Services\Project\CategoryService;
use App\Core\Services\Setting\SettingsService;
use App\Exception\AppException;
use App\Job\TaskJob;
use App\Mail\Redmine\ProjectsProgressMail;
use App\Model\Model;
use App\Model\Redmine\CategoryModel;
use App\Model\Redmine\ProductModel;
use App\Model\Redmine\ProductPlatformModel;
use App\Model\Redmine\ProjectsCustomizedModel;
use App\Model\Redmine\ProjectsExtModel;
use App\Model\Redmine\ProjectsInfoModel;
use App\Model\Redmine\ProjectsProgressModel;
use App\Model\Redmine\ProjectsProgressDetailsModel;
use App\Model\Redmine\ProjectsWatcherModel;
use App\Model\Redmine\MemberModel;
use App\Model\Redmine\ProjectModel;
use App\Model\Redmine\UserModel;
use App\Model\TchipSale\FollowDescTableModel;
use App\Model\TchipSale\FollowTableModel;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use App\Core\Services\Queue\Redmine\ProjectsProgressWxQueue;
use App\Core\Services\AuthService;
use HyperfExt\Mail\Mail;
use App\Model\Redmine\UserCommentsModel;
use App\Core\Services\Notice\Driver\DynamicNoticeFactory;

/**
 * 项目跟进信息
 */
class ProjectsProgressService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var ProjectsProgressModel
     */
    protected $model;

    /**
     * @Inject()
     * @var ProjectsProgressWxQueue
     */
    protected $projectsProgressWxQueue;

    /**
     * @Inject()
     * @var AuthService
     */
    protected $authService;

    public function getOverView($id)
    {
        $row = $this->model::query()->with(['details', 'createUser', 'project'])->find($id);
        $row = $row ? $row->toArray() : [];
        if ($row) {
            if (!empty($row['details'])) {
                foreach ($row['details'] as &$detail) {
                    $detail['diff_value_text'] = $this->getDetailsAttr($detail['property'], $detail['prop_key'], $detail['old_value'], $detail['value']);
                }
            }
            $row['type_text'] = CategoryModel::query()->where('type', 'project_progress_type')
                ->where('keywords', $row['type'])->value('name');
        }

        return $row;
    }

    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        return $query->with(['responsibleUser', 'createUser', 'journal'])->orderBy($sort, $order)->paginate($limit);
    }

    public function doEdit(int $id, array $values)
    {
        // 是否需要推送消息
        $isMessagePush = true;
        $result = Db::connection(DataBaseCode::TCHIP_REDMINE)->transaction(function () use ($id, &$values, &$isMessagePush) {
            $userCommentsModel = make(UserCommentsModel::class);
            // 处理通知人员
            if (!empty($values['notification_member']) && !empty($values['notification_type'])) {
                foreach ($values['notification_type'] as $ntype) {
                    if ($ntype == 'mail') {
                        $values['mail_user'] = array_values($values['notification_member']);
                    } else if ($ntype == 'workwx') {
                        $values['workwx_user'] = array_values($values['notification_member']);
                    }
                }
                unset($values['notification_member']);
                unset($values['notification_type']);
            }

            $values['workwx_user'] = !empty($values['workwx_user']) ? $values['workwx_user'] : [];
            $values['mail_user'] = !empty($values['mail_user']) ? $values['mail_user'] : [];

            $projectInfo = ProjectsInfoModel::query()->where('project_id', $values['project_id'])->first();
            if (!empty($projectInfo->product_manager_uid) ) {
                // 通知人都为空时，或者存在需要通知的企微成员时加入产品负责人
                if (!empty($values['mail_user']) && !empty($values['workwx_user'])) {
                    $values['mail_user'][] = $projectInfo->product_manager_uid;
                    $values['workwx_user'][] = $projectInfo->product_manager_uid;
                    $values['mail_user'] = array_unique($values['mail_user']);
                    $values['workwx_user'] = array_unique($values['workwx_user']);
                } else if (!empty($values['mail_user']) && !in_array($projectInfo->product_manager_uid, $values['mail_user'])) {
                    //1. 存在邮箱通知时判断是否需要加入产品负责人
                    $values['mail_user'][] = $projectInfo->product_manager_uid;
                } else if (!empty($values['workwx_user']) && !in_array($projectInfo->product_manager_uid, $values['workwx_user'])) {
                    //2. 存在企微通知时判断是否需要加入产品负责人
                    $values['workwx_user'][] = $projectInfo->product_manager_uid;
                } else {
                    //3. 其它情况一律加入
                    $values['workwx_user'][] = $projectInfo->product_manager_uid;
                    $values['mail_user'][] = $projectInfo->product_manager_uid;
                }
            }

            // 判断是否变更通知，默认加上所有成员
            if (!empty($values['type']) && $values['type'] == 10) {
                $members = make(MemberService::class)->getProjectMembers($values['project_id']);
                $membersIds = $members ? array_unique(array_column($members, 'user_id')) : [];
                $values['workwx_user'] = array_merge($values['workwx_user'], $membersIds);
                $values['mail_user'] = array_merge($values['mail_user'], $membersIds);
            }

            if (empty($values['workwx_user']) && empty($values['mail_user'])) {
                $isMessagePush = false;
            }

            // 修改属性
            $details = [];
            $productProgressDetailsModel = make(ProjectsProgressDetailsModel::class);
            if (!empty($values['details'])) {
                $details = $values['details'];
                unset($values['details']);
            }
            if ($id > 0) {
                if (!isset($values['pid'])|| $values['pid'] == 0) {
                    $row = $this->model::query()->find($id);
                } else {
                    $row = $userCommentsModel::query()->find($id);
                }
                if (!$row) {
                    throw new AppException(StatusCode::ERR_SERVER, __('exception.err_sqlerror'));
                }
                $result = $row->update($values);
                // 大于5才获取实例，否则提交附件一次就通知一次，需要等到按通知按钮才通知
                if ($result && count($values) > 5) {
                    $result = $this->model::query()->find($id);
                }
            } else {
                $values['create_user_id'] = $values['create_user_id'] ?? getRedmineUserId();
                if( !isset($values['pid'])|| $values['pid'] == 0){
                    $result = $this->model::query()->create($values);
                }else{
                    $result = $userCommentsModel::query()->create($values);
                }
            }

            foreach ($details as &$detail) {
                $detail['progress_id'] = $result->id;
                $detail['old_value'] = $detail['old_value'] ?? '';
                // Db::connection(DataBaseCode::TCHIP_REDMINE)->insert("insert into product_progress_details (`property`, `prop_key`, `old_value`, `value`, `progress_id`, `content`, `updated_at`, `created_at`) values ('{$detail['property']}', '{$detail['prop_key']}', '{$detail['old_value']}', '{$detail['value']}', '{$detail['progress_id']}', CURRENT_TIMESTAMP(5), CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())");
                $productProgressDetailsModel::query()->create($detail);
            }
            return $result;
        });
        
        // 推送通知事件
        if (!empty($result->id)) {
            if (!empty($values['description'])) {
                // 存在description值
                if ($isMessagePush) {
                    // make(\App\Core\Services\Notice\NoticeService::class)->projectProgress($result->id); // 跟进内容发送

                    $progress = ProjectsProgressModel::query()
                        ->with(['project', 'projectExt'])
                        ->find($result->id);

                    if ($progress) {
                        $type = $progress->projectExt->project_type == ProjectCode::PROJECT_TYPE_PROD ? 'ProductProgressNotice' : 'ProjectProgressNotice';
                        // DynamicNoticeFactory 统一调用
                        DynamicNoticeFactory::call($type, $result->id, $progress);
                    }
                }
                // 如果是评论内容
                if (!empty($values['pid'])) {
                    make(\App\Core\Services\Notice\NoticeService::class)->atProjectComment($result->id, $values);
                } else {
                    // 正常回复
                    make(\App\Core\Services\Notice\NoticeService::class)->atProjectProgress($result->id, $values);
                }
                // 跟进回复通知
                if(isset($values['pid']) && $values['pid'] != 0){
                    make(\App\Core\Services\Notice\NoticeService::class)->recoverProduct($values);
                }
            } else {
                // description为空
                if ($isMessagePush) {
                    $this->projectsProgressWxQueue->push(['progress_id' => $result->id, 'product_id' => $values['project_id']]); // 属性修改发送
                }
            }
        }
        return $result;
    }

    /**
     * 删除操作
     * @param $ids
     * @return int
     */
    public function doDelete($ids): int
    {
        $result = Db::connection(DataBaseCode::TCHIP_REDMINE)->transaction(function () use ($ids) {
            $ids = explode(',', $ids);
            foreach ($ids as $id) {
                $result = $this->model::destroy($id);
                if ($result) {
                    \App\Model\Redmine\ProjectsProgressDetailsModel::query()->where('progress_id', $id)->delete();
                }
            }
        });
        return 1;
    }

    public function productFollowList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10, $page = 1)
    {
        // 未指定产品ID，查询该用户下所有产品
        $ruid = getRedmineUserId();
        $accountService = make(\App\Core\Services\Redmine\AccountService::class);
        if (empty($filter['project_id'])) {
            if ((!$this->authService->isSuper() && !$accountService->isAdmin()) || (!empty($filter['is_watcher']) && $filter['is_watcher'] == 1)) {
                $memberProducts = MemberModel::query()->where('user_id', $ruid)->pluck('project_id');
                $watcherProducts = ProjectsWatcherModel::query()->where('user_id', $ruid)->pluck('project_id');
                $filter['project_id'] = array_merge(($memberProducts ? $memberProducts->toArray() : [] ), ($watcherProducts ? $watcherProducts->toArray() : []));
                if ($filter['project_id']) {
                    $filter['project_id'] = implode(',', $filter['project_id']);
                    $op['project_id'] = 'IN';
                } else {
                    $filter['project_id'] = 0;
                }
            }
        } else {
            $product = ProjectModel::query()->select(['projects.*', 'projects_ext.relation_product_id'])->join('projects_ext', 'projects.id', '=', 'projects_ext.project_id')
                ->where('projects.id', $filter['project_id'])
                ->first();
            $product = $product ? $product->toArray() : [];
            if (!empty($product['relation_product_id'])) {
                $product['relation_product_id'] = !is_array($product['relation_product_id']) ? json_decode($product['relation_product_id'], true) : $product['relation_product_id'];
            }
            if (!empty($product['relation_product_id']) && empty($filter['version']) && empty($filter['version_pre'])) {
                $filter['project_id'] = implode(',', array_merge((!is_array($filter['project_id']) ? explode(',', $filter['project_id']) : $filter['project_id']), $product['relation_product_id']));
                $op['project_id'] = 'IN';
            }
        }
        if (!empty($filter['is_watcher'])) {
            unset($filter['is_watcher']);
        }

        $visibleNotice = ProjectCode::PROGREES_VISIBLE_NOTICE;

        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        // 可见性条件
        if (!$this->authService->isSuper() && !$accountService->isAdmin()) {
            $query = $query->whereRaw("((visible = {$visibleNotice} and (create_user_id = '{$ruid}' or JSON_CONTAINS(mail_user->'$[*]', '{$ruid}') or JSON_CONTAINS(workwx_user->'$[*]', '{$ruid}'))) or visible = 1)");
        }
        $paginate = $query->orderBy($sort, $order)->paginate($limit);
        $paginate = $paginate ? $paginate->toArray() : [];



        if (!empty($paginate['data'])) {
            // 获取上级回复数据
            $pids = array_filter(array_column($paginate['data'], 'pid'));
            $usernameSql = "id, pid, create_user_id, type, description, created_at, IF(username != '', username, (SELECT CONCAT(lastname, firstname) as username from users where id = projects_progress.create_user_id)) as username";
            $parents = $this->model::query()->selectRaw($usernameSql)->whereIn('id', $pids)->get();
            $parents = $parents ? array_column($parents->toArray(), null, 'id') : [];

            $productIds = array_values(array_unique(array_column($paginate['data'], 'project_id')));
            $productsInfo = ProjectModel::query()->whereIn('id', $productIds)->get();
            $productsInfo = $productsInfo ? array_column($productsInfo->toArray(), null, 'id') : [];
            // foreach ($paginate['data'] as &$datum) {
            //     $datum['product'] = $productsInfo[$datum['product_id']] ?? [];
            //     if (!empty($datum['pid']) && !empty($parents[$datum['pid']])) {
            //         $datum['parent'] = $parents[$datum['pid']];
            //     } else {
            //         $datum['parent'] = null;
            //     }
            // }

            // 获取跟进details
            $ids = array_column($paginate['data'], 'id');
            $details = ProjectsProgressDetailsModel::query()->whereIn('id', $ids)->orderBy('created_at', 'DESC')->get()->toArray();

            $messageUsers = $this->handleMessageUsers($paginate['data']);
            $messageUsers = $messageUsers ? array_column($messageUsers, null, 'id') : [];

            $typeList = make(\App\Core\Services\Project\CategoryService::class)->typeChildrenList('project_progress_type');
            $typeList = $typeList ? array_column($typeList, null, 'keywords') : [];

            // 获取被通知人的头像信息
            foreach ($paginate['data'] as &$item) {
                $item['details'] = $this->getDetails($item['id'], $details);
                $item['users'] = !empty($messageUsers[$item['create_user_id']]) ? $messageUsers[$item['create_user_id']] : null;
                $item['product'] = $productsInfo[$item['project_id']] ?? [];
                if (!empty($item['pid']) && !empty($parents[$item['pid']])) {
                    $item['parent'] = $parents[$item['pid']];
                } else {
                    $item['parent'] = null;
                }
                $item['notice_user_message'] = [];
                $messageUids = array_merge(($item['workwx_user'] ?? []), ($item['mail_user'] ?? []));
                foreach ($messageUids as $user) {
                    if (isset($messageUsers[$user])) {
                        $item['notice_user_message'][$user] = $messageUsers[$user];
                    }
                }
                $item['type_text'] = $typeList[$item['type']]['name'] ?? '';
            }
        }
        return $paginate;
    }


    public function getDetailsAttr($property, $propKey, $oldValue, $value)
    {
        // $fieldList = $this->getAttrNameList();
        // // foreach ($details as &$detail) {
        // $detail['prop_name'] = $fieldList[$detail['prop_key']] ?? $detail['prop_key'];
        if ($property == 'attr' || $property == 'ext_attr') {
            switch ($propKey) {
                case 'client_id':
                    $oldValueText = $oldValue;
                    $valueText = $value;
                    break;
                case 'charge_id':
                case 'hard_handler_uid':
                case 'soft_handler_uid':
                    $oldUser = UserModel::query()->where('id', $oldValue)->first();
                    $oldValueText = $oldUser ? ($oldUser->lastname . $oldUser->firstname) : '-';
                    $user = UserModel::query()->where('id', $value)->first();
                    $valueText = $user ? ($user->lastname . $user->firstname) : '-';
                    break;
                case 'hard_handler_id':
                case 'soft_handler_id':
                    $oldValue = !is_array($oldValue) ? json_decode($oldValue, true) : $oldValue;
                    $value = !is_array($value) ? json_decode($value, true) : $value;
                    $ids = array_merge($oldValue, $value);
                    $users = UserModel::query()->whereIn('id', $ids)->get();
                    $oldValueText = '';
                    $valueText = '';
                    foreach ($users as $user) {
                        if (in_array($user->id, $oldValue)) {
                            $oldValueText = $oldValueText ? $oldValueText . ', ' . $user->name : $user->name;
                        }
                        if (in_array($user->id, $value)) {
                            $valueText = $valueText ? $valueText . ', ' . $user->name : $user->name;
                        }
                    }
                    break;
                case 'project_id':
                    $oldValue = $oldValue ? (!is_array($oldValue) ? json_decode($oldValue, true) : $oldValue) : [];
                    $value = $value ? ( !is_array($value) ? json_decode($value, true) : $value) : [];
                    $ids = array_merge($oldValue ?? [], $value ?? []);
                    if ($ids) {
                        $projects = ProjectModel::query()->whereIn('id', $ids)->get();
                        $oldValueText = '';
                        $valueText = '';
                        foreach ($projects as $project) {
                            if (in_array($project->id, $oldValue)) {
                                $oldValueText = $oldValueText ? $oldValueText . ', ' . $project->name : $project->name;
                            }
                            if (in_array($project->id, $value)) {
                                $valueText = $valueText ? $valueText . ', ' . $project->name : $project->name;
                            }
                        }
                    }
                    break;
                case 'platform_id':
                    $oldPlatform = ProductPlatformModel::query()->where('id', $oldValue)->first();
                    $oldValueText = $oldPlatform ? $oldPlatform->name : '-';
                    $platform = ProductPlatformModel::query()->where('id', $value)->first();
                    $valueText = $platform ? $platform->name : '-';
                    break;
                case 'status':
                    $statuList = make(CategoryService::class)->typeChildrenList('product_status');
                    $statuList = $statuList ? array_column($statuList, null, 'keywords') : [];
                    $oldValueText = $statuList[$oldValue]['name'] ?? '-';
                    $valueText = $statuList[$value]['name'] ?? '-';
                    break;
                case 'product_type':
                    $oldType = CategoryModel::query()->where('id', $oldValue)->first();
                    $oldValueText = $oldType ? $oldType->name : '-';
                    $type = CategoryModel::query()->where('id', $value)->first();
                    $valueText = $type ? $type->name : '-';
                    break;
                case 'website_cn':
                case 'website_en':
                case 'taobao':
                case 'demo':
                case 'sale':
                case 'online_info':
                case 'img_material':
                case 'wiki':
                case 'rom':
                case 'specification_cn':
                case 'specification_en':
                case 'amazon':
                case 'tmall':
                case 'shop_en':
                    $settingService = make(SettingsService::class);
                    $statuList = $settingService->statusList('product_attr_status');
                    $statuList = $statuList ? array_column($statuList, null, 'value') : [];
                    $oldValueText = $statuList[$oldValue]['name'] ?? '-';
                    $valueText = $statuList[$value]['name'] ?? '-';
                    break;
                case 'online_status':
                    $categoryService = make(CategoryService::class);
                    $statuList = $categoryService->lists('product_online_status');
                    $statuList = $statuList ? array_column($statuList, null, 'id') : [];
                    $oldValueText = $statuList[$oldValue]['name'] ?? '-';
                    $valueText = $statuList[$value]['name'] ?? '-';
                    break;
                case 'progress':
                    $oldValueText = $oldValue ? $oldValue . '%' : '0%';
                    $valueText = $value ? $value . '%' : '0%';
                    break;
                default:
                    $oldValueText = $oldValue ?? '-';
                    $valueText = $value;
            }
        } else if ($property == 'desc_attr') {
            if (strpos($propKey, '_link') !== false) {
                $oldValueText = $oldValue ?? '-';
                $valueText = $value;
            } else {
                $oldValueText = CategoryModel::query()->where('keywords', $oldValue)->where('type', 'product_desc_status')->value('name');
                $valueText = CategoryModel::query()->where('keywords', $value)->where('type', 'product_desc_status')->value('name');
            }
        }else if ($property == 'json') {
            $oldValueText = json_decode($oldValue, true);
            $valueText = json_decode($value, true);
            foreach ($oldValueText as $tkey => $tvalue) {
                if ($tvalue == $valueText[$tkey]) {
                    unset($oldValueText[$tkey]);
                    unset($valueText[$tkey]);
                }
            }
            $oldValueText = array_values($oldValueText);
            $valueText = array_values($valueText);
        } else {
            $oldValueText = $oldValue ?? '-';
            $valueText = $value;
        }
        // }
        return [$oldValueText, $valueText];
    }

    /**
     * 获取修改的类型名称
     * @param $propKey
     * @return string
     */
    public function getAttrNameList($propKey): string
    {
        $fieldList = [
            'name'                  => '产品名称',
            'client_id'             => '客户',
            'charge_id'             => '负责人',
            'platform_id'           => '产品平台',
            'status'                => '状态',
            'itemtype'              => '项目类型',
            'plantype'              => '方案类别',
            'view_state'            => '查看权限',
            'relationpro'           => '所属产品',
            'customer'              => '顾客名称',
            'product_type'          => '产品类型',
            'yewu_handler'          => '业务员',
            'hard_handler_uid'      => '硬件负责人',
            'soft_handler_uid'      => '软件负责人',
            'hard_handler_id'       => '硬件负责人',
            'soft_handler_id'       => '软件负责人',
            'creator'               => '创建人',
            'created_date'          => '立项日期',
            'closed_date'           => '归档日期',
            'progress'              => '进度',
            'progress_status'       => '进度状态',
            'description'           => '描述',
            'website_cn'            => '中文官网',
            'website_en'            => '英文官网',
            'taobao'                => '淘宝',
            'demo'                  => '样机',
            'sale'                  => '销售',
            'online_info'           => '上线信息状态',
            'img_material'          => '图片素材状态',
            'wiki'                  => '维基',
            'rom'                   => '固件',
            'specification_cn'      => '中文规格书状态',
            'specification_en'      => '英文规格书状态',
            'specification'         => '规格书',
            'amazon'                => '亚马逊状态',
            'tmall'                 => '天猫状态',
            'shop_en'               => '英文商城',
            'website_cn_link'       => '中文官网地址',
            'website_en_link'       => '英文官网地址',
            'taobao_link'           => '淘宝地址',
            'demo_link'             => '样机地址',
            'sale_link'             => '销售地址',
            'online_info_link'      => '上线信息地址',
            'img_material_link'     => '图片素材地址',
            'wiki_link'             => '维基地址',
            'rom_link'              => '固件地址',
            'specification_cn_link' => '中文规格书地址',
            'specification_en_link' => '英文规格书地址',
            'amazon_link'           => '亚马逊地址',
            'tmall_link'            => '天猫地址',
            'shop_en_link'          => '英文商城地址',
            'online_status'         => '上线状态',
            'online_note'           => '上线注备',
            'official_desc'         => '官网',
            'sale_desc'             => '销售平台',
            'launch_desc'           => '上线准备',
            'project_id'            => '关联项目',
            'copywriting'           => '文案',
            'website_img'           => '网页图',
            'shelves_img'           => '上架图',
            'package'               => '包装',
            'wiki_cn'               => '维基中',
            'wiki_en'               => '维基英',
            'wiki_cn_link'          => '维基中地址',
            'wiki_en_link'          => '维基中地址',
            'outside_datum'         => '外发资料',



        ];


        $extList = array_column(ProductCode::EXT_FIELD, 'text', 'value');
        $fieldList = array_merge($fieldList, $extList);
        return $fieldList[$propKey] ?? '';
    }

    protected function getAttrDescFieldName($field)
    {
        $arr = [
            'color' => '颜色',
            'url'   => '地址',
            'text'  => '描述'
        ];
        return $arr[$field] ?? $field;
    }

    /**
     * 检测修改的数据是否需要推送事件
     * @param $productId
     * @param $productProgressId
     * @param array $attrs
     * @return void
     */
    protected function productEditEvent($productId, $productProgressId, array $attrs)
    {
        $onlineAttr = ['website_cn', 'website_en', 'taobao', 'demo', 'sale', 'online_info', 'img_material', 'wiki', 'rom', 'specification_cn', 'specification_en', 'amazon', 'tmall', 'shop_en', 'online_status', 'online_note'];
        $isEvent = false;
        foreach ($attrs as $attr) {
            if (!empty($attr['prop_key']) && in_array($attr['prop_key'], $onlineAttr)) {
                $isEvent = true;
                break;
            }
        }
        if ($isEvent) {
            // 这里 dispatch(object $event) 会逐个运行监听该事件的监听器
            $this->productEditQueue->push([
                'product_id' => $productId,
            ]);
        }
    }

    /**
     * 处理用户信息
     * @param $data
     * @return void
     */
    protected function handleMessageUsers($data)
    {
        $uids = [];
        $users = array_column($data, 'mail_user');
        $users = $users ? : [];
        $workwxUser = array_column($data, 'workwx_user');
        $users = array_merge($users, $workwxUser ? : []);
        foreach ($users as $eu) {
            if ($eu) {
                $uids = array_merge($uids, $eu);
            }
        }
        $createUser = array_column($data, 'create_user_id');
        $uids = array_unique(array_merge($uids, $createUser));
        return \App\Model\Redmine\UserModel::query()->whereIn('id', $uids)->get()->toArray();

    }

    /**
     * 获取进跟details
     * @param $progressId
     * @param $details
     * @return void
     */
    protected function getDetails($progressId, array $details)
    {
        $list = [];
        foreach ($details as $detail) {
            if ($detail['progress_id'] == $progressId) {
                $list[] = $detail;
            }
        }
        return $list;
    }

    /**
     * 将关联产品的值所在字段relation_projects_id改为relation_product_id
    * @return void
     */
    public function swapRelationPorjectAndProductId()
    {
        // 批量转到relation_product_id
       \App\Model\Redmine\ProjectsExtModel::query()->where('project_type', '=', 'product_type')
       ->update(['relation_product_id' => \Hyperf\DbConnection\Db::raw('relation_project_id')]);

       // 清空relation_project_id
        \App\Model\Redmine\ProjectsExtModel::query()->where('project_type', '=', 'product_type')
            ->update(['relation_project_id' => null]);

    }

    /**
     * 销售系统定制项目跟进信息迁移到BI项目跟进
     * @param null $names
     * @return void
     */
    public function migrateSaleFollowDetailsToProjectProgress($names = null)
    {
        $productModel = make(ProjectModel::class);
        $productCustomizedModel = make(ProjectsCustomizedModel::class);
        $followModel = make(FollowTableModel::class);
        $followDescModel = make(FollowDescTableModel::class);
        $userService = make(\App\Core\Services\UserService::class);
        $productTable = 'projects';
        $infoTable = 'projects_info';
        $followDescTable = 'follow_desc_table';
        $followTable = 'follow_table';
        $saleUserTable = 'user_table';
        $saleClientConTable = 'client_contact_table';
        $saleClientTable = 'client_table';
        $userList = [];

        if (!empty($names)) {
            $names = !is_array($names) ? explode(',', $names) : $names;
        }
        $products = $productModel::query()->select(["{$productTable}.*", "{$infoTable}.client_name"])->join($infoTable, "{$productTable}.id", '=', "{$infoTable}.project_id")
            ->where("{$infoTable}.category", 'customized')
            ->where(function ($query) use ($names) {
                if ($names) {
                    $query->whereIn('name', $names);
                }
                return $query;
            })
            ->get();
        if ($products) {
            $products = $products->toArray();

            foreach ($products as $product) {
                // 同步客户名称，客户ID，产品ID
                if (empty($products['client_name'])) {
                    $follow = $followModel::query()->select(["{$followTable}.*", "{$saleClientTable}.name", "{$saleClientConTable}.comy"])
                        ->join($saleClientTable, "{$followTable}.clientid", '=', "{$saleClientTable}.id")
                        ->join($saleClientConTable, "{$followTable}.clientid", '=', "{$saleClientConTable}.client_id")
                        ->where('projectname', $product['name'])->first();
                    if ($follow) {
                        $clientName = $follow['comy'] ?? $follow['name'];
                        $productCustomizedModel::query()->where('project_id', $product['id'])
                            ->update(
                                ['sale_client_id' => $follow['clientid'], 'client_name' => $clientName, 'relation_sale_product_id' => $follow['relationpro']]
                            );
                    }
                }

                // 存在跟进暂时不再同步操作，避免重复
                if ($this->model::query()->where('project_id', $product['id'])->whereNotNull('description')->count()) continue;
                $saleProgress = $followDescModel::query()->select(["{$followDescTable}.*", "{$saleUserTable}.username"])->join($followTable, "{$followDescTable}.followid", '=', "{$followTable}.id")
                    ->join($saleUserTable, "{$followDescTable}.userid", '=', "{$saleUserTable}.id")
                    ->where("{$followTable}.projectname", $product['name'])->get();
                if ($saleProgress) {
                    $saleProgress = $saleProgress->toArray();
                    foreach ($saleProgress as $progress) {
                        if (empty($userList[$progress['username']])) {
                            $userList[$progress['username']] = $userService->userInfoByThirdByName($progress['username']);
                        }

                        $saveProgress = [
                            'project_id' => $product['id'],
                            'description' => $progress['followinfo'],
                            'type' => ProductCode::PROGRESS_TYPE['progress']['value']
                        ];
                        if (!empty($userList[$progress['username']])) {
                            $saveProgress['create_user_id'] = $userList[$progress['username']]['third_user_id'];
                        } else {
                            $saveProgress['username'] = $progress['username'];
                        }
                        $result = $this->doEdit(0, $saveProgress);
                        if (!empty($result->id)) {
                            if (!empty($progress['time']) && $progress['time'] > 0) {
                                $this->model::query()->where('id', $result->id)->update(['created_at' => date('Y-m-d H:i:s', $progress['time'])]);
                            }

                        }
                    }
                }
            }
        }
        return true;
    }


    /**
     * 1. 直接把参数发送到异步
     * 2. 导步中处理数据,再调用通用service的函数
     * @param $userIds
     * @param $progress
     * @return bool
     */
    public function sendEmailProgress($userIds, $progress)
    {
        $userIds = is_array($userIds) ? $userIds : explode(',', (string)$userIds);
        $mailUser = \App\Model\TchipBi\UserModel::query()->select('user.*')->join('user_third', 'user.id', '=', 'user_third.user_id')
            ->whereIn('user_third.third_user_id', $userIds)->where('user_third.platform', 'redmine')
            ->where('user.status', 1)->get();
        foreach ($mailUser as &$user) {
            $user->product_id = $progress['project']['id'];
            $user->product_progress = $progress;
            $mail = make(ProjectsProgressMail::class, [$user]);
            Mail::to($user->biz_mail)->send($mail);
        }
        return true;
    }
}
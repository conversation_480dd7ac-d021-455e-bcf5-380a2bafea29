<?php
    /**
     *
     * @Copyright T-chip Team.
     * @Date 2022/6/22 下午2:50
     * <AUTHOR>
     * @Description
     *
     */

    namespace App\Core\Services\Project;

    use App\Constants\DataBaseCode;
    use App\Constants\IssueCode;
    use App\Constants\StatusCode;
    use App\Core\Services\Project\CheckList\CheckListService;
    use App\Core\Services\TestPlan\TestPlanCaseService;
    use App\Core\Utils\Log;
    use App\Core\Utils\TimeUtils;
    use App\Core\Utils\Tree;
    use App\Exception\AppException;
    use App\Model\Redmine\AttachmentModel;
    use App\Model\Redmine\CustomFieldsModel;
    use App\Model\Redmine\CustomValuesModel;
    use App\Model\Redmine\FlowModel;
    use App\Model\Redmine\IssueCategoriesModel;
    use App\Model\Redmine\IssueAssignedModel;
    use App\Model\Redmine\IssueClassModel;
    use App\Model\Redmine\IssueModel;
    use App\Model\Redmine\IssueRelationModel;
    use App\Model\Redmine\IssuesExtModel;
    use App\Model\Redmine\IssueStatusModel;
    use App\Model\Redmine\JournalDetailsModel;
    use App\Model\Redmine\JournalExtModel;
    use App\Model\Redmine\MemberModel;
    use App\Model\Redmine\MemberRolesModel;
    use App\Model\Redmine\ProjectModel;
    use App\Model\Redmine\ProjectsExtModel;
    use App\Model\Redmine\TokenModel;
    use App\Model\Redmine\TrackersModel;
    use App\Model\Redmine\UserModel;
    use App\Model\Redmine\VersionModel;
    use App\Model\Redmine\WatchersModel;
    use App\Model\Redmine\WorkflowsModel;
    use Carbon\Carbon;
    use Hyperf\Context\Context;
    use Hyperf\Database\Model\Builder;
    use Hyperf\Database\Model\Collection;
    use Hyperf\DbConnection\Db;
    use Hyperf\Di\Annotation\Inject;
    use App\Model\Redmine\JournalsModel;
    use Hyperf\Utils\ApplicationContext;
    use Parsedown;
    use Qbhy\HyperfAuth\AuthManager;
    use Hyperf\Utils\Coroutine;
    use Hyperf\Utils\Parallel;
    use League\HTMLToMarkdown\HtmlConverter;
    use Swoole\Coroutine\Channel;
    use Hyperf\Paginator\Paginator;

    class IssueService extends \App\Core\Services\BusinessService
    {
        /**
         * @Inject()
         * @var IssueModel
         */
        protected $model;

        /**
         * @Inject()
         * @var \App\Core\Services\Redmine\IssueService
         */
        protected $redmineIssueService;

        /**
         * @Inject()
         * @var AuthManager
         */
        protected $auth;

        /**
         * @Inject()
         * @var CheckListService
         */
        protected $checkListService;

        public $isRelation = true;


        public function flattenArray(array $data, string $childrenKey = 'children'): array
        {
            $flattened = [];

            foreach ($data as $item) {
                $currentItem = $item;
                unset($currentItem[$childrenKey]); // 去掉子级数据，避免无限嵌套
                $flattened[] = $currentItem;

                if (isset($item[$childrenKey]) && is_array($item[$childrenKey])) {
                    // 递归展平子级数据
                    $flattened = array_merge($flattened, $this->flattenArray($item[$childrenKey], $childrenKey));
                }
            }

            return $flattened;
        }


        public function rebuildTreeAndPaginate($query, $limit, $sort, $order, $currentPage, $filterByProjectType, $projectType, $notExitInIssueAssignedTable, $needRelations = null)
        {

            $this->buildIssueListQuery($query, $sort, $order, $notExitInIssueAssignedTable, []);
            $query = $query->select(['id',
                'subject',
                'parent_id',
                'fixed_version_id',
                'created_on',
            ]);
            // buildIssueListQuery中获取的watch_timing未带出 需要再addSelect否则无watch_timing可order
            if ($sort === 'watch_timing') {
                $userId = getRedmineUserId();
                $query = $query->addSelect([Db::raw("(SELECT id FROM watchers WHERE watchers.watchable_id = issues.id AND watchers.user_id = {$userId} ORDER BY id DESC LIMIT 1) as watch_timing")]);
            }
            $query = $query->get();

            $paginate['data'] = $query ? $query->toArray() : []; // select * 会导致转数组开销大

            // 其他时候树状列表 搜索时候平铺
            if (( (empty($filter['subject']))  || !empty($filter['fixed_version_id']))) {
                // 筛选规划列表下内容
                // if (isset($filter['fixed_version_id']) && $filter['fixed_version_id'] != 'IS NULL') {
                //     $paginate['data'] = collect($paginate['data'])
                //         ->filter(function ($item) use ($filter) {
                //             return $item['fixed_version_id'] == $filter['fixed_version_id'];
                //         })
                //         ->all();
                // }

                if (isset($filter['fixed_version_id']) && $filter['fixed_version_id'] != 'IS NULL') {
                    $fixedVersionIds = is_array($filter['fixed_version_id']) ? 
                        $filter['fixed_version_id'] : 
                        explode(',', $filter['fixed_version_id']);
                    
                    $paginate['data'] = collect($paginate['data'])
                        ->filter(function ($item) use ($fixedVersionIds) {
                            return in_array($item['fixed_version_id'], $fixedVersionIds);
                        })
                        ->all();
                }

                //// 处理列表附加属性
                //$this->formatIssueData($paginate['data']);

                $minId = ($paginate && $paginate['data'] ? min(array_column($paginate['data'], 'parent_id')) : 0) ?? 0;
                $paginate['data'] = make(Tree::class)->getTreeListV2($paginate['data'], $minId, 'parent_id', 'id');

            }

            $totalItems = count($paginate['data']);

            // 定义每页显示的条目数和当前页码
            $itemsPerPage = $limit; // 每页显示的条目数
            $page = $currentPage ?? 1; // 当前页码

            // 计算起始索引
            $startIndex = ($page - 1) * $itemsPerPage;


            // 使用 array_slice 进行分页
            $pagedData = array_slice($paginate['data'], $startIndex, $itemsPerPage);


            ///////////////////////////////////////////////////////////////
            // 20250108 在分页后处理，否则查询事项数量过多 //////////////////////
            $flattenedData = $this->flattenArray($pagedData);
            $issueIds = array_column($flattenedData, 'id');

            $finalQuery = make(IssueModel::class)::query()->whereIn('id', $issueIds);
            $this->buildIssueListQuery($finalQuery, $sort, $order, $notExitInIssueAssignedTable, $needRelations);
            $finalData = $finalQuery ? $finalQuery->get()->toArray() : [];
            // 处理列表附加属性
            $this->formatIssueData($finalData);
            $finalData = make(Tree::class)->getTreeListV2($finalData, $minId, 'parent_id', 'id');
            $pagedData = $finalData;
            //////////////////////////////////////////////////////////////


//        $pagedData = $paginate['data'];

            // 计算当前页的信息
            $totalPages = ceil($totalItems / $itemsPerPage);

            // 构建分页信息数组
            $paginationInfo = [
                "current_page" => $page,
                "data" => $pagedData,
                "from" => $startIndex + 1,
                "last_page" => $totalPages,
                "per_page" => $itemsPerPage,
                "to" => min($startIndex + $itemsPerPage, $totalItems),
                "total" => $totalItems
            ];
            return $paginationInfo;
        }

        public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
        {
            $request = \Hyperf\Utils\ApplicationContext::getContainer()->get(\Hyperf\HttpServer\Contract\RequestInterface::class);
            $currentPage = $request->input('pageNo') ?? 1;
            //---------------------------------获取关联测试计划的事项-------------------------
            if(!empty($filter['plan_id'])){
                $issueIdArr = make(TestPlanCaseService::class)->getIssueId($filter['plan_id']);
                if($issueIdArr){
                    $filterId = implode(',',array_unique($issueIdArr));
                    $filter['id'] = $filterId;
                    $op['id'] = 'in';
                }else{
                    $filter['id'] = 0;
                }
            }
            unset($filter['plan_id']);
            //-----------------------------------------------------------------------------


            // 20250108 依据事项分类class_id 获取
            $rebuildTree = false;
            if (isset($filter['class_id']) && $filter['class_id'] == -2) {
                unset($filter['class_id']);
                $rebuildTree = true;
            }
            if (!empty($filter['class_id'])) {
                // 不包含 class_id 为 0 的， ‘未分类’放行通过
                $classPath = make(IssueClassModel::class)::query()->where('id', $filter['class_id'])->value('path');
                $classIds = make(IssueClassModel::class)::query()->where('path', 'like', $classPath . '%')->pluck('id')->toArray();
                $classIdsString = implode(',', $classIds);
                $filter['class_id'] = $classIdsString;
                $op['class_id'] = 'in';
            }

            $hasCategoryId = false;
            if (isset($filter['category_id'])) {
                $hasCategoryId = true;
            }

            $sortBy = null;
            if ($sort == 'enumerations.position') {
                $sort = 'id';
                $sortBy = 'enumerations.position';
            }
            // 真 我的事项
            $is_my_work = false;
            if (isset($filter['is_my_work'])) {
                $is_my_work = true;
                unset($filter['is_my_work']);
            }

            if (isset($filter['subject']) && $filter['subject'] == "") {
                unset($filter['subject']);
            }
            // 处理人选择时 或 我的事项
            $assignedId = null;
            $myWork = false;
            $treeLike = null;
            // 是否选择树形列表
            if (isset($filter['is_tree_like'])) {
                if ($filter['is_tree_like'] == "true") {
                    $treeLike = true;
                    // 需要过滤版本的时候
//                if (!empty($filter['fixed_version_id'])) {
////                    unset($filter['parent_id']);
//                }
                } else {
                    $treeLike = false;
                }
                unset($filter['is_tree_like']);
            }
            // 20241023 补充多人指派的筛选‘未指定’选项逻辑
            $notExitInIssueAssignedTable = false;
            if (isset($filter['issueAssigned']['user_id'])) {
                if ($filter['issueAssigned']['user_id'] == -1) {
                    $notExitInIssueAssignedTable = true;
                    unset($filter['issueAssigned']);
                    unset($op['issueAssigned']);
                }
            }

            if (isset($filter['assigned_to_id'])) {
                // 处理人未指定
                if ( $filter['assigned_to_id'] == -1) {
                    $filter['assigned_to_id'] = 'IS NULL';
                    $assignedId = -1;
                }
                else if ( empty($filter['fixed_version_id'])) {
                    if (empty($filter['assigned_to_id']) && empty($filter['author_id'])) {
                        $filter['parent_id'] = "IS NULL";
                    }

                    $assignedId = $filter['assigned_to_id'];
                    $myWork = true;
                }
                else {
                    $assignedId = $filter['assigned_to_id'];
                }
            }
            if ($sort == 'id' || !$sort) {
                $sort = 'created_on';
            }
            // 筛选了状态
            $status_selected = false;
            if (isset($filter['status_selected'])) {
                $status_selected = true;
                unset($filter['status_selected']);
            }

            // 如果按照项目所属类型来筛选（issues表不能加字段原因）
            $filterByProjectType = false;
            $projectType = '';
            if (!empty($filter['project_type'])) {
                $filterByProjectType = true;
                $projectType = $filter['project_type'];
                unset($filter['project_type']);
            }


            if ($treeLike && (!empty($filter['author_id']) || $rebuildTree || (isset($filter['class_id']) && $filter['class_id'] >= 0) || !empty($filter['assigned_to_id']) || !empty($filter['fixed_version_id'])
                    || !empty($filter['watcher']['watchable_type']))) {
                if ( !empty($filter['fixed_version_id'])) {
                    unset($filter['parent_id']);
                }
                if ($rebuildTree || (isset($filter['class_id']) && $filter['class_id'] >= 0)) {
                    unset($filter['parent_id']);
                    $needRelations = [
                        'categoryText',
                        //'issueExt',
                        //'attachment',
                        //'issueStatus',
                        'projectText:id,name',
                        'authorText',
                        //'enumerations',
                        'issueAssigned'
                    ];
                }
                list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
                $paginate = $this->rebuildTreeAndPaginate($query, $limit, $sort, $order,  $currentPage, $filterByProjectType, $projectType, $notExitInIssueAssignedTable, $needRelations ?? null);


                return $paginate;
            }
            list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);

            empty($op['just_need_issue_pure_list']) && $this->buildIssueListQuery($query, $sort, $order, $notExitInIssueAssignedTable,  [
                'categoryText',
                'projectText:id,name',
                'authorText',
                'issueAssigned'
            ]);
            $paginate = $query->paginate($limit);
            $paginate = $paginate ? $paginate->toArray() : [];

            $getType = Context::get('getIssueList');
            if (!$getType || $getType != 'task') {
                $isLogin = $this->auth->check();
            } else {
                $isLogin = false;
            }
            // 非搜索和指定处理人时候查询上下级任务
            if ($limit != 999 && $treeLike && empty($filter['subject']) && ($myWork  || ($assignedId == null )) && !$is_my_work) {
                $paginate['data'] = $this->getParentsAndChildren($paginate['data'], 4, $assignedId, $filter, $op);

            }

            // 去除重复数组元素
            $paginate['data'] = collect($paginate['data'])
                ->unique('id')
                ->values()
                ->all();

            $startTime = microtime(true);
            // 获取无父事项的子事项，只在第一页获取数据时执行，防止后续页重复获取
            $pageNo = $paginate['current_page'];
//        if ($treeLike && !empty($filter['fixed_version_id']) && $pageNo == 1) {
            if ($treeLike && $pageNo == 1) {
                // 获取在该filter下的所有事项id
                $parents = $this->getAllIssues($filter, $op, $sort, $order);
                unset($filter['parent_id']); // 此为需要
                $childs = $this->getAllIssues($filter, $op, $sort, $order, 9999, true);
                $childIds = array_column($childs, 'parent_id');
                $parentIds = array_column($parents, 'id');
                $getDiff = array_diff($childIds, $parentIds);
                // 获取子事项中parent_id不在$parents的id里面的项
                $childIds = array_filter($childIds, function ($item) use ($getDiff) {
                    return in_array($item, $getDiff);
                });
                // 获取得回子事项
                $trueChild= array_filter($childs, function ($item) use ($childIds) {
                    return in_array($item['parent_id'], $childIds);
                });
                $childIds = array_column($trueChild, 'id');
                // 除 所有事项 外都获取一次
                if (!empty($childIds) && (isset($filter['fixed_version_id']) || $is_my_work || isset($filter['author_id']) || isset($filter['watcher']) || $status_selected || $hasCategoryId)) {
                    $filter['id'] =  implode(',', $childIds);
                    $op['id'] = 'IN';
                    $getChild = $this->getIsNotNull($filter, $op, $sort, $order, 9999);
                    $paginate['data'] = array_merge($paginate['data'], $getChild);
                }
            }
            $paginate['data'] = collect($paginate['data'])
                ->unique('id')
                ->values()
                ->all();

            // 筛选规划列表下内容
            if (isset($filter['fixed_version_id']) && $filter['fixed_version_id'] != 'IS NULL') {
                $paginate['data'] = collect($paginate['data'])
                    ->filter(function ($item) use ($filter) {
                        return $item['fixed_version_id'] == $filter['fixed_version_id'];
                    })
                    ->all();
            }

//        // 获取所得列表中没有父任务的子任务
//        $tmpChildList = collect($paginate['data'])->filter(function ($item) {
//            return $item['parent_id'] != null;
//        })
//            ->all();
//        $excludedParentIds = collect($paginate['data'])->pluck('id')->all();
//        $tmpChildList = collect($tmpChildList)->reject(function ($item) use ($excludedParentIds) {
//            return $item['parent_id'] == null || in_array($item['parent_id'], $excludedParentIds);
//        })->all();

            // 处理列表附加属性
            empty($op['just_need_issue_pure_list']) && $this->formatIssueData($paginate['data']);

            $minId = ($paginate && $paginate['data'] ? min(array_column($paginate['data'], 'parent_id')) : 0) ?? 0;
            // 其他时候树状列表 搜索时候平铺
            if ($treeLike && ($is_my_work ||  (empty($filter['subject'])))) {
                // 存在parent_id的子任务但其父事项不在所得数组中，该子任务会被抛弃，需要另做处理
                $paginate['data'] = make(Tree::class)->getTreeListV2($paginate['data'], $minId, 'parent_id', 'id');
            }
//        $paginate['data'] = array_merge($paginate['data'], $tmpChildList);
            // 去除重复数组元素
            $paginate['data'] = collect($paginate['data'])
                ->unique('id')
                ->values()
                ->all();


            // 对结果排序处理
            if ($order == 'ASC' || $order == 'DESC') {
                switch ($sort) {
                    case 'created_on':
                    case 'updated_on':
                    case 'due_date':
                    case 'assigned_to_id':
                    case 'watch_timing':
                        $direction = ($order == 'ASC') ? '' : 'Desc';
                        $paginate['data'] = collect($paginate['data'])
                            ->{"sortBy$direction"}($sort)
                            ->values()
                            ->all();
                        break;
                    default:
                        if ($sortBy) {
                            $paginate['data'] = collect($paginate['data'])
                                ->sortByDesc('priority_position')
                                ->values()
                                ->all();
                        }
                }
            } else {
                $paginate['data'] = collect($paginate['data'])
                    ->sortByDesc('created_on')
                    ->values()
                    ->all();
            }

            // $paginate['category_data'] = $category_data;
            return $paginate;
        }

        public function getAllList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC')
        {
            // 默认分页限制
            $limit = 99999;
            if (!empty($filter['limit'])) {
                $limit = $filter['limit'];
                unset($filter['limit']);
            }

            // 获取页码，默认为第一页
            $pageNo = 1;
            if (!empty($filter['pageNo'])) {
                $pageNo = $filter['pageNo'];
                unset($filter['pageNo']);
            }

            // 是否需要分页
            $needPaginate = false;
            if (!empty($filter['needPaginate'])) {
                $needPaginate = true;
                unset($filter['needPaginate']);
            }

            $asRelation = false;
            if (!empty($filter['as_relation'])) {
                $asRelation = true;
                unset($filter['as_relation']);
            }

            $onlyGetProductType = false;
            if (!empty($filter['onlyGetProductType'])) {
                $onlyGetProductType = true;
                unset($filter['onlyGetProductType']);
            }

            // 初始化查询对象
            $query = IssueModel::query();
            $number = 0;

            // 处理 subject 参数
            if(!empty($filter['subject'])) {
                if (preg_match('/#(\d+)/', $filter['subject'], $matches)) {
                    $number = (int)$matches[1];
                } else if (preg_match('/^\d{1,7}$/', $filter['subject'])) {
                    $number = (int)$filter['subject'];
                }

                // 如果提取到了ID号，修改filter使用ID查询
                if (!empty($number)) {
                    // 添加ID条件
                    $filter['id'] = $number;
                    $op['id'] = '='; // 使用精确匹配
                    
                    // 清除subject条件，避免干扰
                    unset($filter['subject']);
                    if (isset($op['subject'])) {
                        unset($op['subject']);
                    }
                }
            }

            // 获取用户加入的项目列表
            $myProject = make(\App\Core\Services\Project\ProjectService::class)->getJoinedProjectList(getRedmineUserId(), $onlyGetProductType);
            $myProjectIds = array_column($myProject['data'], 'id');

            // 构建查询条件
            list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, $query);

            // 查询并设置排序
            $query = $query->select(['issues.*'])
                ->with(['categoryText','issueExt', 'attachment', 'issueStatus', 'assignedText', 'projectText', 'authorText', 'enumerations', 'issueAssigned'])
                ->with('versionText:id,name')
                ->with('issueType:id,name')
                ->whereIn('project_id', $myProjectIds)
                ->orderBy($sort, $order);

            // 使用 paginate 进行分页，传递正确的页码（'page'）和 limit
            $query = $needPaginate ? $query->paginate($limit, ['*'], 'pageNo', $pageNo) : $query->get();

            // 将结果转换为数组
            $rows = $query->toArray() ?? [];
            if (!empty($rows['data'])) {
                $rows['data'] = array_map(function($item) {
                    // 确保 'issue_assigned' 是一个数组
                    if (!empty($item['issue_assigned']) && is_array($item['issue_assigned'])) {
                        // 如果是数组，获取 'user_id' 列表
                        $item['assigned'] = array_column($item['issue_assigned'], 'user_id');
                    } else {
                        // 确保 'assigned' 为空数组，而不是标量
                        $item['assigned'] = [];
                    }
                    return $item;
                }, $rows['data']);
                $rows['data'] = array_values($rows['data']);
            }


            // 搜索相关事项
            if (!$asRelation || ($asRelation && !empty($number) && (!empty($filter['id']) && $filter['id'] != $number))) {
                $queryUseId = $this->model::query()->where('id', $number)
                    ->whereIn('project_id', $myProjectIds)->first();

                if ($queryUseId) {
                    $row = $queryUseId->toArray();
                    array_unshift($rows, $row);
                }
            }

            return $rows;
        }

        public function issueRelationsList($issueId, array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 99999)
        {
            //var_export($filter);
            $relations = IssueRelationModel::query()->where('issue_from_id', $issueId)->orWhere('issue_to_id', $issueId)->get()->toArray();
            $relIds = array_filter(array_merge(array_column($relations, 'issue_from_id'), array_column($relations, 'issue_to_id')));
            if ($relIds) {
                list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
                // $query = $query->select(['issues.*', 'issue_relations.id as relation_id', 'issue_relations.relation_type', 'issue_relations.delay']);
                $query = $query
                    ->with(['categoryText','issueExt', 'attachment', 'issueStatus', 'assignedText', 'projectText', 'authorText', 'enumerations', 'issueAssigned'])
                    ->with('versionText:id,name')
                    ->with('issueType:id,name')
                    ->whereIn('id', $relIds)
                    ->where('id', '<>', $issueId)
                    // ->join('issue_relations', 'issues.id', '=', 'issue_relations.issue_to_id')
                    //
                    // ->where('issue_relations.issue_from_id', $issueId)->orWhere('issue_to_id', $issueId)
                    // ->orWhere(function ($query) use ($issueId) {
                    //     $query->where('issue_to_id', $issueId)->where('issue_to_id', '<>', $issueId);
                    // } )
                    ->orderBy($sort, $order);
                $paginate = $query->paginate($limit);
                $paginate = $paginate ? $paginate->toArray() : [];

                $paginate['data'] = array_map(function($item) {
                    $item['assigned'] = !empty($item['issue_assigned']) ? array_column($item['issue_assigned'], 'user_id') : [];
                    return $item;
                }, $paginate['data']);

                if (!empty($paginate['data'])) {
                    $formRows = array_column($relations, null, 'issue_from_id');
                    $toRows = array_column($relations, null, 'issue_to_id');
                    foreach ($paginate['data'] as &$datum) {
                        if (!empty($formRows[$datum['id']])) {
                            $datum['relation_id'] = $formRows[$datum['id']]['id'];
                            $datum['relation_type'] = $formRows[$datum['id']]['relation_type'];
                            $datum['relation_delay'] = $formRows[$datum['id']]['delay'];
                        } else if (!empty($toRows[$datum['id']])) {
                            $datum['relation_id'] = $toRows[$datum['id']]['id'];
                            $datum['relation_type'] = $toRows[$datum['id']]['relation_type'];
                            $datum['relation_delay'] = $toRows[$datum['id']]['delay'];
                        }
                    }
                }
                return $paginate;
            }
            return [];
        }

        public function getAllIssues(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 9999, bool $getChild = false)
        {
            $query = $this->model::query();
            list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, $query);
            $query = $query->select('issues.id');
            if ($getChild) {
                $query = $query
                    ->addselect('issues.parent_id')
                    ->whereNotNull('issues.parent_id');
            }
            $query = $query->get();
            return $query ? $query->toArray() : [];
        }


        public function getIsNotNull(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 9999, $startDate = null, $endDate = null) {
            $query = $this->model::query();
            list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, $query);
//        $query = $query->select(['issues.*', 'enumerations.position', 'enumerations.name as priority_text']);
            $query = $query->select(['issues.*']);
            if ($startDate !== null && $endDate !== null) {
                $query = $query->whereBetween('created_on', [$startDate, $endDate]);
            }
            $query = $query
                ->whereNotNull('issues.parent_id')
                ->with(['categoryText', 'issueExt', 'attachment', 'issueStatus', 'assignedText', 'projectText', 'authorText', 'enumerations'])
                ->with('versionText:id,name')
//            ->leftJoin('enumerations', 'issues.priority_id', '=', 'enumerations.id')
                ->orderBy($sort, $order);
            $paginate = $query->paginate($limit);
//        $paginate = $query->get();
            $paginate = $paginate ? $paginate->toArray() : [];
            return $paginate['data'];
        }

        public function getParentsAndChildren($data, $maxDepth, $assignedId, $filter, $op)
        {
            if (isset($filter['subject'])) {
                unset($filter['subject']);
            }
            if (isset($filter['parent_id'])) {
                unset($filter['parent_id']);
            }
            $result = [];
            $result = array_merge($result, $data);
            $tmpDepth1 = $maxDepth;
            $tmpData = $data;
            $tmpFixedVersionId = null;
            $tmpStatusId = null;

            // 获取父事项
            if (isset($filter['fixed_version_id'])) {
                $tmpFixedVersionId = $filter['fixed_version_id'];
//            unset($filter['fixed_version_id']);
            }
            if (!empty($filter['status_id'])) {
                $tmpStatusId = $filter['status_id'];
            }
            while (!empty($tmpData)) {
                $pids = array_filter(array_column($tmpData, 'parent_id'));
                if ($pids == 0 || !$pids) {
                    break;
                }
                $tmpFilter =array_merge($filter, ['id' => implode(',', $pids)]);
                $tmpData = $this->lists($tmpFilter, ['id' => 'OR']);
//            $tmpData = $this->lists(['id' => implode(',', $pids)], ['id' => 'OR']);
                if ($tmpData) {
                    $result = array_merge($result, $tmpData);
                }
            }
            // 获取子事项
            $tmpData2 = [];
            $filter['status_id'] = $tmpStatusId;
            $filter['fixed_version_id'] = $tmpFixedVersionId;
            while ($tmpDepth1 >= 0 && !empty($data)) {
                $ids = array_column($data, 'id');
                $data = $this->model::query();
                list($data, $limit, $sort, $order) = $this->buildparams($filter,$op, [], [], [],  $data);
                // 循环内先单独获取id， 后面再一次性查出
                $rows = $data
                    ->select('id')
                    ->whereIn('parent_id', $ids);
//            if ($assignedId != null) {
//                if ($assignedId != -1) {
//                    $rows = $rows->where('assigned_to_id', $assignedId);
//                } else {
//                    $rows = $rows->where('assigned_to_id', 'IS NULL');
//                }
//            }
                $rows = $rows->get();
                $data = $rows ? $rows->toArray() : [];
                if ($data) {
                    $tmpData2 = array_merge($tmpData2, $data);
                    $tmpDepth1--;
                } else {
                    break;
                }
            }

//         根据子事项id一次性查出所有数据
            $ids = array_column($tmpData2, 'id');
            if (!empty($tmpData2) && !empty($ids)) {
                $batchSize = 15; // 定义批处理的大小
                // 将ID分成批次
                $batches = array_chunk($ids, $batchSize);
                // 创建一个通道用于接收结果
                $resultChannel = new Channel();
                foreach ($batches as $batch) {
                    co(function () use ($batch, $resultChannel) {
                        try {
                            // 执行查询，处理当前批次的数据
                            $res = $this->model::query()
//                            ->select('issues.*', 'enumerations.position', 'enumerations.name as priority_text')
                                ->select('issues.*')
                                ->with([
                                    'issueAssigned',
                                    'categoryText',
                                    'attachment',
                                    'issueStatus',
                                    'assignedText',
                                    'projectText',
                                    'authorText',
                                    'issueExt',
                                    'versionText:id,name',
                                    'enumerations'
                                ])
//                            ->leftJoin('enumerations', 'issues.priority_id', '=', 'enumerations.id')
                                ->whereIn('issues.id', $batch)
                                ->orderBy('created_on', 'desc')
                                ->get()
                                ->toArray();

                            $res = array_map(function($item) {
                                $item['assigned'] = !empty($item['issue_assigned']) ? array_column($item['issue_assigned'], 'user_id') : [];
                                return $item;
                            }, $res);
                            // 将结果放入通道
                            if ($res) {
                                $resultChannel->push($res);
                            }
                        } catch(AppException $e) {
                            $res = null;
                        }

                    });
                }
                foreach ($batches as $batch) {
                    $batchResult = $resultChannel->pop();
                    if ($batchResult) {
                        $result = array_merge($result, $batchResult);
                    }
                }
                $resultChannel->close();
            }
            return $result;
        }

        public function getListv2(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
        {
            if ($sort == 'id' || !$sort) {
                $sort = 'created_on';
            }
            $startDate = null;
            $endDate = null;
            if (!empty($filter['created_on'])) {
                list($startDate, $endDate) = explode(' - ', $filter['created_on']);
                // 格式化日期时间字符串为正确的格式
                $startDate = date('Y-m-d H:i:s', strtotime($startDate . ' 00:00:00'));
                $endDate = date('Y-m-d H:i:s', strtotime($endDate . ' 23:59:59'));
                unset($filter['created_on']);
            }
            // if (empty($filter['parent_id'])) {
            //     $filter['parent_id'] = 'IS NULL';
            //     $op['parent_id'] = 'IS NULL';
            // }
            $query = $this->model::query();

            /* 暂时不使用多人指派 */
            // if (!empty($filter['assigned_to_id'])) {
            //     $query = $query->where(function ($query) use($filter){
            //         /** @var $query IssueModel */
            //         $assigedId = (!is_array($filter['assigned_to_id']) ? explode(',', $filter['assigned_to_id']) : $filter['assigned_to_id']);
            //         $query->whereIn('assigned_to_id', $assigedId)
            //             ->orWhereExists(function ($query) use($filter, $assigedId){
            //                 $query->select()->from('custom_values')->whereRaw('issues.id = customized_id')->where('custom_field_id', IssueCode::MULTI_ASSIGN_ID)->whereIn('value', $assigedId);
            //             });
            //     });
            //     unset($filter['assigned_to_id']);
            // }
            /* 暂时不使用多人指派 */

            list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, $query);
//        $query = $query->select(['issues.*', 'enumerations.position', 'enumerations.name as priority_text']);
            $query = $query->select(['issues.*']);
            if ($startDate !== null && $endDate !== null) {
                $query = $query->whereBetween('created_on', [$startDate, $endDate]);
            }
            $query = $query
                // ->with(['issueExt', 'attachment', 'issueStatus', 'assignedText', 'projectText', 'authorText', 'customFieldsMultiAssignId' => function ($query) {
                //     $query->where('custom_field_id', IssueCode::MULTI_ASSIGN_ID);
                // }])
                ->with(['categoryText', 'issueExt', 'attachment', 'issueStatus', 'assignedText', 'projectText', 'authorText', 'enumerations'])
                ->with('versionText:id,name')
//            ->leftJoin('enumerations', 'issues.priority_id', '=', 'enumerations.id')
                ->orderBy($sort, $order);
            // if ($sort == 'enumerations.position') {
            //     $query = $query->orderBy($sort, $order)->orderBy('created_on', 'DESC');
            // } else if ($sort == 'created_on') {
            //     $query = $query->orderBy($sort, $order)->orderBy('enumerations.position', 'DESC');
            // } else {
            //     $query = $query->orderBy('enumerations.position', 'DESC')->orderBy('created_on', 'DESC')->orderBy($sort, $order);
            // }
            $paginate = $query->paginate($limit);
            $paginate = $paginate ? $paginate->toArray() : [];

            // $customFieldsMultiAssignIds = array_column($paginate['data'], 'custom_fields_multi_assign_id');
            // $customFieldsMultiAssignIds = array_merge([], ...$customFieldsMultiAssignIds);
            // $customFieldsMultiAssignIds = array_column($customFieldsMultiAssignIds, 'value');
            // $members = UserModel::query()->whereIn('id', $customFieldsMultiAssignIds)->get();
            // $members = $members ? array_column($members->toArray(), null, 'id') : [];

            $getType = Context::get('getIssueList');
            if (!$getType || $getType != 'task') {
                $isLogin = $this->auth->check();
            } else {
                $isLogin = false;
            }

            // 子任务列表
            if ($paginate['data']) {
                $ids = array_column($paginate['data'], 'id');
                $childList = $this->lists(['parent_id' => implode(',', $ids)], ['parent_id' => 'IN']);
                $childList = $childList ? : [];
                $childListKeys = [];
                foreach ($childList as &$cItem) {
                    if ($cItem['description']) {
                        $cItem['description'] = $this->formatIssueDesc($cItem['id'], $cItem['description']);
                    }
                    if ($isLogin) {
                        $newStatusList       = $this->getNewIssueStatus(null, $cItem['project_id'], $cItem['tracker_id'], $cItem['status_id'], $cItem['author_id'], $cItem['assigned_to_id']);
                        $cItem['status_list'] = $newStatusList;
                    }
                    if (empty($cItem['issue_ext'])) {
                        $cItem['issue_ext'] = [
                            'release_version_id' => 0
                        ];
                    }

                    $cItem['created_on_text'] = date('Y-m-d', strtotime($cItem['created_on']));
                    $childListKeys[$cItem['parent_id']][] = $cItem;
                }
            }

            // description
            foreach ($paginate['data'] as &$pval) {
                if ($isLogin) {
                    $newStatusList       = $this->getNewIssueStatus(null, $pval['project_id'], $pval['tracker_id'], $pval['status_id'], $pval['author_id'], $pval['assigned_to_id']);
                    $pval['status_list'] = $newStatusList;
                }

                if (empty($pval['issue_ext'])) {
                    $pval['issue_ext'] = [
                        'release_version_id' => 0
                    ];
                }

                $pval['created_on_text']                 = date('Y-m-d', strtotime($pval['created_on']));
                // $customFieldsMultiAssignIdItem           = [];
                // $pval['custom_fields_multi_assign_text'] = '';
                // foreach ($pval['custom_fields_multi_assign_id'] as $aid) {
                //     if (!empty($aid['value'])) {
                //         $customFieldsMultiAssignIdItem[] = $aid['value'];
                //     }
                //     if (!empty($members[$aid['value']])) {
                //         $pval['custom_fields_multi_assign_text'] .= !$pval['custom_fields_multi_assign_text'] ? $members[$aid['value']]['name'] : ', ' . $members[$aid['value']]['name'];
                //     }
                // }
                // $pval['custom_fields_multi_assign_id'] = $customFieldsMultiAssignIdItem;

                // 注入子任务列表
                if (!empty($childListKeys[$pval['id']])) {
                    $pval['children'] = $childListKeys[$pval['id']];
                } else {
                    $pval['children'] = [];
                }
            }

            //获取事项的父事项
            $this->getParentIssue($paginate['data'],  $isLogin, $sort);
            return $paginate;
        }

        public function getParentIssue(&$data, $isLogin, $sort)
        {
            $parentIds = array_filter(array_column($data, 'parent_id'));
//        $parentsWithChildren = $this->model::query()->whereIn('id', $parentIds)->get()->toArray();
            $parentsWithChildren = $this->model::query()
//            ->select(['issues.*', 'enumerations.position', 'enumerations.name as priority_text'])
                ->select(['issues.*'])
                ->with(['categoryText', 'issueExt', 'attachment', 'issueStatus', 'assignedText', 'projectText', 'authorText', 'customFieldsMultiAssignId' => function ($query) {
                    $query->where('custom_field_id', IssueCode::MULTI_ASSIGN_ID);
                }, 'enumerations'])
                ->with('versionText:id,name')
//            ->leftJoin('enumerations', 'issues.priority_id', '=', 'enumerations.id')
                ->whereIn('issues.id', $parentIds)->orderBy($sort, 'DESC')->get()->toArray();
            foreach ($parentsWithChildren as $pkey => &$value) {
                if ($isLogin) {
                    $newStatusList = $this->getNewIssueStatus(null, $value['project_id'], $value['tracker_id'], $value['status_id'], $value['author_id'], $value['assigned_to_id']);
                    $value['status_list'] = $newStatusList;
                }
                $value['created_on_text'] = date('Y-m-d', strtotime($value['created_on']));
                $customFieldsMultiAssignIdItem           = [];
                $value['custom_fields_multi_assign_text'] = '';
                foreach ($value['custom_fields_multi_assign_id'] as $aid) {
                    if (!empty($aid['value'])) {
                        $customFieldsMultiAssignIdItem[] = $aid['value'];
                    }
                    if (!empty($members[$aid['value']])) {
                        $value['custom_fields_multi_assign_text'] .= !$value['custom_fields_multi_assign_text'] ? $members[$aid['value']]['name'] : ', ' . $members[$aid['value']]['name'];
                    }
                }
                $value['custom_fields_multi_assign_id'] = $customFieldsMultiAssignIdItem;
            }
            $parentsWithChildren = array_column($parentsWithChildren, null, 'id');
            foreach ($data as $pkey => &$value) {
                if ($isLogin) {
                    $newStatusList = $this->getNewIssueStatus(null, $value['project_id'], $value['tracker_id'], $value['status_id'], $value['author_id'], $value['assigned_to_id']);
                    $value['status_list'] = $newStatusList;
                }
                $parentId = $value['parent_id'];
                if ($parentId !== null && isset($parentsWithChildren[$parentId])) {
                    // 如果父事项已存在，将子事项数据合并到父事项的 children 键中
                    $parentsWithChildren[$parentId]['children'][] = $value;
                } elseif ($parentId === null) {
                    // 如果没有父事项，直接将子事项添加到结果数组
                    $parentsWithChildren[] = $value;
                }
            }
            $data = $parentsWithChildren;
            $uniqueData = [];
            $uniqueId = [];
            // 现在全部是父事项了，过滤掉重复的父事项（选父事项里子事项数量多的保留）
            foreach ($data as $item) {
                $itemId = $item['id'];
                if (isset($uniqueId[$itemId])) {
                    $existingItem = $uniqueData[$itemId];
                    if (is_array($existingItem['children']) && count($existingItem['children']) > count($item['children'])) {
                        continue;
                    }
                }
                $uniqueId[$itemId] = 1;
                $uniqueData[$itemId] = $item;
            }

            // 将去重后的项目数组转回索引数组
            $data = array_values($uniqueData);
            // 按日期排序
            usort($data, function ($a, $b){
                $timestampA = strtotime($a['created_on']);
                $timestampB = strtotime($b['created_on']);
                if ($timestampA == $timestampB) {
                    return 0; // 不需要交换位置
                }
                return ($timestampA > $timestampB) ? -1 : 1; // 降序
            });
        }
        public function lists(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC')
        {
            if ($sort == 'id' || !$sort) {
                $sort = 'created_on';
            }
            if (empty($filter['parent_id'])) {
                $filter['parent_id'] = 'IS NULL';
                $op['parent_id'] = 'IS NULL';
            }
            if (isset($filter['isSendEmail']) && $filter['isSendEmail']) {
                unset($filter['parent_id']);
                unset($filter['isSendEmail']);
            }
            // 弃用assigned_to_id
            $checkAssignedId = null;
            if (!empty($filter['assigned_to_id'])) {
                $checkAssignedId = $filter['assigned_to_id'];
            }
            $query = $this->model::query();
            if ((!empty($filter['parent_id']) && !empty($filter['id'])) &&
                ((!empty($op['parent_id']) && $op['parent_id'] == 'OR') || (!empty($op['id']) && $op['id'] == 'OR'))) {
                $parentIds = is_array($filter['parent_id']) ? $filter['parent_id'] : explode(',', $filter['parent_id']);
                $ids = is_array($filter['id']) ? $filter['id'] : explode(',', $filter['id']);
                $query->where(function ($query) use ($parentIds, $ids, $checkAssignedId) {
                    /* @var IssueModel $query */
                    $query->whereIn('issues.parent_id', $parentIds)
                        ->orWhereIn('issues.id', $ids);
                    // 这里的$parentIds是上级的ids传过来，所以这里需要过虑避免数据重复
                } )->whereNotIn('issues.id', $parentIds);
                unset($filter['parent_id'], $filter['id']);
            }

            if ($checkAssignedId) {
                unset($filter['assigned_to_id']);
                $query->where(function($query) use ($checkAssignedId) {
                    $query->whereExists(function ($subQuery) use ( $checkAssignedId) {
                        $subQuery->selectRaw(1)
                            ->from('issue_assigned')
                            ->whereColumn('issue_assigned.issue_id', 'issues.id')
                            ->where('issue_assigned.user_id', $checkAssignedId)
                            ->whereNull('issue_assigned.deleted_at');
                    });
                });
            }

            list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, 9999, $query);
            $rows = $query
//            ->select(['issues.*', 'enumerations.position', 'enumerations.name as priority_text'])
                ->select(['issues.*'])
                ->with(['categoryText', 'attachment', 'issueStatus', 'assignedText', 'projectText', 'authorText', 'issueExt', 'enumerations'])
                ->with('versionText:id,name')
                ->with('issueAssigned')
//            ->leftJoin('enumerations', 'issues.priority_id', '=', 'enumerations.id')
                ->orderBy($sort, $order)->get();
            $rows = $rows ? $rows->toArray() : [];
            return $rows;
        }

        /**
         * 子工作项列表的获取 只获取必要展示部分
         * @param array $filter
         * @param array $op
         * @param string $sort
         * @param string $order
         * @return array
         */
        public function getAllChildList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC')
        {
            $query = $this->model::query();
            list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, 9999, $query);
            $rows = $query
                ->select(['id', 'subject', 'tracker_id', 'due_date', 'status_id', 'assigned_to_id', 'priority_id', 'project_id', 'author_id', 'parent_id', 'fixed_version_id'])
                ->with(['issueStatus', 'assignedText', 'enumerations', 'issueAssigned', 'projectText'])
                ->orderBy($sort, $order)->get();
            $rows = $rows ? $rows->toArray() : [];
            $rows = array_map(function($item) {
                $item['assigned'] = !empty($item['issue_assigned']) ? array_column($item['issue_assigned'], 'user_id') : [];
                return $item;
            }, $rows);
            return $rows;
        }

        /**
         * *递归查找子事项列表，用‘level’记录层级，以平铺方式返回
         * @param $parent_id
         * @param int $level
         * @return array
         */
        public function getAllChild($parent_id, int $level = 0, array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC')
        {

            $issueAssignedId = null;
            if (isset($filter['issueAssigned']['user_id'])) {
                $issueAssignedId = $filter['issueAssigned']['user_id'];
                unset($filter['issueAssigned']);
                unset($op['issueAssigned']);
            }

            $result = [];
            if ($parent_id) {
                $filter['parent_id'] = $parent_id;
                $children = $this->getAllChildList($filter, $op, $sort, $order);
                if ($children) {
                    foreach ($children as $child) {
                        $child['level'] = $level;
                        $childResult = $this->getAllChild($child['id'], $level + 1, $filter, $op, $sort, $order);
                        $result = array_merge($result, [$child], $childResult);
                    }
                } else {
                    return [];
                }
            }

            // 在返回前过滤 data
            if ($issueAssignedId !== null) {
                $result = array_filter($result, function ($item) use ($issueAssignedId) {
                    return isset($item['assigned']) && in_array($issueAssignedId, $item['assigned']);
                });
                $result = array_values($result);
            }

            return $result;
        }

        /**
         * 获取所有子事项只有事项数据
         * @param array $issueIds 父事项ID数组
         * @return array 所有子事项数据
         */
        public function getAllChildIssues(array $issueIds)
        {
            $childs = $this->model::query()
                ->whereIn('parent_id', $issueIds)
                ->select(['id', 'subject', 'tracker_id', 'status_id', 'priority_id', 'category_id',
                         'fixed_version_id', 'assigned_to_id', 'start_date', 'due_date', 'class_id',
                         'parent_id', 'project_id', 'author_id', 'description'])
                ->get();
            $childs = $childs ? $childs->toArray() : [];
            if ($childs) {
                $childIds = array_column($childs, 'id');
                $nextChilds = $this->getAllChildIssues($childIds);
                if ($nextChilds) {
                    $childs = array_merge($childs, $nextChilds);
                }
            }
            return $childs;
        }

        /**
         * 详情
         */
        public function getOverView($id)
        {
            $row = $this->model::query()
                ->selectRaw('issues.*, IF(parent_id > 0, (select subject from issues s where s.id = issues.parent_id), \'\') as parent_name')
                ->where('id', $id)
                ->with(['categoryText',  'issueExt', 'attachment', 'issueStatus', 'projectText', 'authorText', 'enumeration', 'issueAssigned',
                    'customValues' => function ($query) {
                        return $query->where('customized_type', 'Issue');
                    },
                    'projectExt',
                    'tracker',
                ])
                ->withCount(['watcher'])->first();
            if ($row) {
                $row                = $row->toArray();
                // $row['assigned']    = !empty($row['issue_assigned']) ? array_column($row['issue_assigned'], 'user_id') : (!empty($row['assigned_to_id']) ? [$row['assigned_to_id']] : []);
                $row['assigned']    = !empty($row['issue_assigned']) ? array_column($row['issue_assigned'], 'user_id') : [];
                $row['is_watcher']  = WatchersModel::query()->where('user_id', getRedmineUserId())->where('watchable_id', $id)->value('id');
                $row['is_watcher']  = $row['is_watcher'] ? 1 : 0;
                $row['status_list'] = $this->getNewIssueStatus(null, $row['project_id'], $row['tracker_id'], $row['status_id'], $row['author_id'], $row['assigned_to_id']);
                $row['journals']    = $this->issueJournals($row['id']);
                $row['children']    = $this->getAllChild($id);
                if ($row['children']) {
                    foreach ($row['children'] as &$child) {
                        $newStatusList       = $this->getNewIssueStatus(null, $child['project_id'], $child['tracker_id'], $child['status_id'], $child['author_id'], $child['assigned_to_id']);
                        $child['status_list'] = $newStatusList;
                    }
                }
                $row['watchers'] = make(\App\Core\Services\Project\WatcherService::class)->getWatcherIds($row['id']);
                // 20240228-15:39 这个操作异常耗时 尚未知道作用暂时注释
//                if ($row['parent_id'] && $row['parent_id'] > 0) {
//                    $row['parent_issue'] = $this->getOverView($row['parent_id']);
//                } else {
//                    $row['parent_issue'] = null;
//                }

                $customField = make(CustomFieldsService::class)->getProjectCustomFieldList($row['project_id'], $row['tracker_id']);
                $row['custom_fields'] = $this->setIssueCustomFields($id, $customField, $row['custom_values']);

                if(!isset($row['issue_ext']['release_version_id'])) {
                    $row['issue_ext']['issue_id'] = $row['id'];
                    $row['issue_ext']['release_version_id'] = 0;
                }

                //
                if (!empty($row['issue_ext']['relation_project_id'])) {
                    $relationProjectFilter['project_id'] = $row['issue_ext']['relation_project_id'];
                    $row['issue_ext']['relation_project_text'] = ProjectModel::query(true)->where('id', $row['issue_ext']['relation_project_id'])->value('name');
                    if (!empty($row['issue_ext']['relation_version_id'])) {
                        $relationProjectFilter['fixed_version_id'] = $row['issue_ext']['relation_version_id'];
                        $row['issue_ext']['relation_version_text'] = VersionModel::query(true)->where('id', $row['issue_ext']['relation_version_id'])->value('name');
                    }
                    $row['issue_ext']['relation_project_todo'] = $this->issueCount(array_merge($relationProjectFilter, ['status_id' => 'todo']));
                    $row['issue_ext']['relation_project_all'] = $this->issueCount($relationProjectFilter);
                }

                // 把多人指派独立出来
                if ($row['custom_fields']) {
                    foreach ($row['custom_fields'] as $custom) {
                        if ($custom['id'] == IssueCode::MULTI_ASSIGN_ID) {
                            $row['custom_fields_multi_assign_id'] = $custom['value'];
                            break;
                        }
                    }
                }
            }
            return $row;
        }

        /**
         * 获取列表
         * @param array $filter
         * @param array $op
         * @param string $sort
         * @param string $order
         * @param int $limit
         * @return mixed
         */
        public function getCategoryList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 9999, $startDate = null, $endDate = null)
        {
            if (empty($filter['project_id'])) {
                return [];
            }
            if (isset($filter['is_tree_like'])) {
                unset($filter['is_tree_like']);
            }
            if (isset($filter['status_selected'])) {
                unset($filter['status_selected']);
            }
            if (isset($filter['category_id'])) {
                unset($filter['category_id']);
            }
            if (isset($filter['is_my_work'])) {
                unset($filter['is_my_work']);
            }
            $notAssigned = false;
            if (isset($filter['issueAssigned']) ) {
                if ($filter['issueAssigned']['user_id'] == -1) {
                    $notAssigned = true;
                    unset($filter['issueAssigned']);
                    unset($op['issueAssigned']);
                } else if (count($filter['issueAssigned']) == 0 || $filter['issueAssigned']['user_id'] == null) {
                    unset($filter['issueAssigned']);
                    unset($op['issueAssigned']);
                }
            }
            $cateTable = 'issue_categories';
            $extTable = 'issue_categories_ext';
            $cateList = IssueCategoriesModel::query()->selectRaw("{$cateTable}.*, IFNULL({$extTable}.position, {$cateTable}.id) as position")
                ->where('project_id', $filter['project_id'])
                ->leftJoin($extTable, "{$cateTable}.id", '=', "{$extTable}.category_id")
                ->withCount(['issues' => function ($query) use ($filter, $op, $notAssigned) {
                    list($query, $limit, $sortx, $order) = $this->buildparams($filter, $op, 'id', 'desc', 999, $query);
                    if ($notAssigned) {
                        $query = $query->whereNotExists(function ($query) {
                            $query->select(DB::raw(1))
                                ->from('issue_assigned')
                                ->whereRaw('issue_assigned.issue_id = issues.id')
                                ->where('issue_assigned.deleted_at', null);
                        });
                    }
                    return $query;
                } ])
                ->orderBy('position', 'asc')->get();
            return $cateList;
            // if (isset($filter['parent_id']) ){
            //     unset($filter['parent_id']);
            // }
            // $query = $this->model::query();
            // list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, $query);
            // $query = $query->select(['issues.subject', 'issues.category_id']);
            // if ($startDate !== null && $endDate !== null) {
            //     $query = $query->whereBetween('created_on', [$startDate, $endDate]);
            // }
            // $query = $query
            //     ->with(['categoryText'])
            //     ->orderBy($sort, $order)->get()->toArray();
            //
            // $query = array_filter($query, function ($value) {
            //     return $value['category_text'] !== null;
            // });
            // $query = array_column((array)$query, 'category_text' , 'id');
            //
            // $queryData = array_count_values(array_column($query, 'id'));
            //
            // $query = array_column($query, null, 'id');
            //
            // return array_map(function ($id, $count) use ($query) {
            //     return ['id' => $id, 'name' => $query[$id]['name'] ?? '', 'category_count' => $count];
            // }, array_keys($queryData), $queryData);
        }

        /**
         * 我的工作数量统计
         * @return array|array[]
         */
        public function myWorkCount($id = null)
        {
            $result = [
                'issue' => [
                    'name' => '未解决',
                    'count' => 0,
                ],
                'watcher' => [
                    'name' => $id ? '关注事项' : '我的关注',
                    'count' => 0,
                ],
                'created' => [
                    'name' => $id ? '创建事项' :'我的创建',
                    'count' => 0,
                ],
                'soon'    => [
                    'name' => '本周到期',
                    'count' => 0,
                ],
                'due'     => [
                    'name' => '已超期限',
                    'count' => 0,
                ],
                'resolved' => [
                    'name' => '已解决',
                    'count' => 0,
                ],
            ];

            try {
                $uid                     = $id ?? getRedmineUserId();
                $selfWeekFirstDay         = TimeUtils::getWeekDay(0);
                $selfWeekLastDay         = TimeUtils::getWeekDay(6);
                $toDay = date('Y-m-d');
                $strStatus = implode(',', make(\App\Core\Services\Project\IssueStatusesService::class)->getToDoStatusIds());
                // 未解决
                $result['issue']['query'] = [
                    'filter' => [
                        'issueAssigned' => ['user_id' => $uid],
                        // 'assigned_to_id' => $uid,
                        'status_id' => $strStatus,
                    ],
                    'op' => [
                        'issueAssigned' => ['user_id' => '='],
                        'status_id' => 'IN',
                    ]
                ];
                // 关注
                $result['watcher']['query'] = [
                    'filter' => [
                        'watcher' => [
                            'user_id' => $uid,
                            'watchable_type' => 'Issue'
                        ],
                    ],
                    'op' => [
                        'watcher' => [
                            'user_id' => '=',
                            'watchable_type' => '='
                        ],
                    ]
                ];
                // 创建
                $result['created']['query'] = [
                    'filter' => [
                        'author_id' => $uid,
//                        'status_id' => $strStatus,
                    ],
                    'op' => [
                        'status_id' => 'IN',
                    ]
                ];
                // 本周
                $result['soon']['query'] = [
                    'filter' => [
                        'assigned_to_id' => $uid,
                        'status_id' => $strStatus,
                        'due_date' => $selfWeekFirstDay .' - '. $selfWeekLastDay,
                    ],
                    'op' => [
                        'status_id' => 'IN',
                        'due_date' => 'DATE',
                    ]
                ];
                // 超时
                $result['due']['query'] = [
                    'filter' => [
                        'assigned_to_id' => $uid,
                        'status_id' => $strStatus,
                        'due_date' => $toDay,
                    ],
                    'op' => [
                        'status_id' => 'IN',
                        'due_date' => '<',
                    ]
                ];
                // 解决
                $result['resolved']['query'] = [
                    'filter' => [
                        'assigned_to_id' => $uid,
                        'status_id' => implode(',', $this->model->status_close),
                    ],
                    'op' => [
                        'status_id' => 'IN',
                    ]
                ];
                // 默认单人处理版
                // $result['issue']['count']   = $this->model::query()->where('assigned_to_id', $uid)->whereIn('status_id', $this->model->status_todo)->count();
                // 2024.10.19 多人处理(issue_assigned表版)
                $result['issue']['count']   = $this->model::query()->whereHas('issueAssigned', function ($query) use ($uid) {
                    $query->where('user_id', $uid);
                })->whereIn('status_id', $this->model->status_todo)->count();

                $result['watcher']['count'] = WatchersModel::query()->where('user_id', $uid)->where('watchable_type', 'Issue')->count();
                $result['created']['count'] = $this->model::query()->where('author_id', $uid)
//                    ->whereIn('status_id', $this->model->status_todo)
                    ->count();
                $result['resolved']['count'] = $this->model::query()->where('assigned_to_id', $uid)
                    ->where('status_id', '<>', '5')
                    ->whereNotIn('status_id', $this->model->status_todo)->count();


                $result['soon']['count']  = $this->model::query()->where('assigned_to_id', $uid)
                    ->whereBetween('due_date', [$selfWeekFirstDay, $selfWeekLastDay])
                    ->whereIn('status_id', $this->model->status_todo)
                    ->count();
                $result['due']['count']     = $this->model::query()->where('assigned_to_id', $uid)
                    ->where('due_date', '<', $toDay)
                    ->whereIn('status_id', $this->model->status_todo)
                    ->count();
            } catch (AppException $e) {

            }
            return $result;
        }

        /**
         * 单事项迁移至其他项目
         */
        public function changeIssueProject($id, $projectId, $versionId = null) 
        {
            DB::beginTransaction();
            try {
                $this->editIssueProject($id, $projectId, $versionId);
                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
            }
            return 'editIssueProject success';
        }

        //单个事项迁移
        public function editIssueProject($id, $projectId, $versionId = null, $first_run = true) 
        {
            // 或需指定redmine数据库
            Db::beginTransaction();
            try{
                $query = $this->model::query()->find($id);
                $oldValue = $query->project_id;
                $oldVersionId = $query->fixed_version_id;
                $data = [
                    'project_id' => $projectId,
                ];
                
                $query->fixed_version_id = null;
                // 被选中迁移项目的事项有父事项则将其父事项取消
                if ($query->parent_id != null && $first_run) {
                    $query->parent_id = null;
                    $query->root_id = $id;
                }

                // 如果指定了版本ID，则设置版本
                if ($versionId !== null) {
                    $query->fixed_version_id = $versionId;
                }else{
                    $query->fixed_version_id = null;
                }


                $query->fill($data);
                $query->save();
                $ids = [];
                $this->getChildId($id, $ids);

                // 获取当前时间
                $currentDateTime = ApplicationContext::getContainer()->get(Carbon::class)->now();

                // 设置时区与redmine同步
                $currentDateTime = $currentDateTime->setTimezone('UTC');

                // 插入到 journals 表中的数据
                $newJournal = new \App\Model\Redmine\JournalsModel();
                $newJournal->journalized_id = $query->id;
                $newJournal->journalized_type = 'Issue';
                $newJournal->user_id = getRedmineUserId();
                $newJournal->notes = '';
                $newJournal->created_on = $currentDateTime;
                $newJournal->private_notes = 0;
                $newJournal->save();

                // 插入到 journal_details 表中的数据
                $newJournalDetail = new \App\Model\Redmine\JournalDetailsModel();
                $newJournalDetail->journal_id = $newJournal->id; // 使用上面插入的 journals 记录的 ID
                $newJournalDetail->property = 'attr'; // 根据你的需求设置相应的值
                $newJournalDetail->prop_key = 'project_id'; // 根据你的需求设置相应的值
                $newJournalDetail->old_value = $oldValue; // 根据你的需求设置相应的值
                $newJournalDetail->value = $projectId; // 根据你的需求设置相应的值
                $newJournalDetail->save(); // 保存记录并返回操作结果

                // 如果版本发生了变化，也记录版本变更
                if ($versionId !== null && $oldVersionId != $versionId) {
                    $versionJournalDetail = new \App\Model\Redmine\JournalDetailsModel();
                    $versionJournalDetail->journal_id = $newJournal->id;
                    $versionJournalDetail->property = 'attr';
                    $versionJournalDetail->prop_key = 'fixed_version_id';
                    $versionJournalDetail->old_value = $oldVersionId;
                    $versionJournalDetail->value = $versionId;
                    $versionJournalDetail->save();
                }

                Db::commit();
            } catch(\Throwable $ex){
                var_dump($ex->getMessage());
                Db::rollBack();
            }

            if (!empty($ids)) {
                foreach ($ids as $childId) {
                    $this->editIssueProject($childId, $projectId, false);
                }
            }
        }

        /**
         * 批量迁移事项至其他项目
         * @param array $issueIds 事项ID数组
         * @param int $projectId 目标项目ID
         * @param int|null $versionId 目标版本ID（可选）
         * @param array $unlinkChildrenIds 需要解除关联的子项ID数组
         * @return string
         */
        public function batchMigrateIssueProject($issueIds, $projectId, $versionId = null, $unlinkChildrenIds = [])
        {
            DB::beginTransaction();
            try {
                // 先解除需要解除关联的子项的父子关系
                if (!empty($unlinkChildrenIds)) {
                    foreach ($unlinkChildrenIds as $childId) {
                        $this->unlinkParentChild($childId);
                    }
                }

                // 然后执行批量迁移
                foreach ($issueIds as $issueId) {
                    $this->migrateIssueProject($issueId, $projectId, $versionId, $issueIds);
                }
                DB::commit();
                return 'batch migrate success';
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        }

        /**
         * 批量复制事项
         * @param array $params 复制参数
         * @return array
         */
        public function batchCopyIssue($params)
        {
            $targetProjectId = $params['target_project_id'];
            $issues = $params['issues'];
            $copyOptions = [
                'copy_attachments' => $params['copy_attachments'] ?? true,
                'link_to_original' => $params['link_to_original'] ?? true,
                'copy_description' => $params['copy_description'] ?? false,
                'copy_children' => $params['copy_children'] ?? false,
                'enable_sync' => $params['enable_sync'] ?? false,
                'copy_checklists' => $params['copy_checklists'] ?? true,
            ];

            // 验证目标项目是否存在
            $targetProject = \App\Model\Redmine\ProjectModel::query()->find($targetProjectId);
            if (!$targetProject) {
                throw new \Exception('目标项目不存在');
            }

            DB::connection('tchip_redmine')->beginTransaction();
            try {
                $copiedIssues = [];
                $originalToNewMapping = []; // 原事项ID到新事项ID的映射
                $failedIssues = []; // 记录复制失败的事项
                $allIssuesToCopy = $issues; // 所有需要复制的事项

                // 如果需要复制子事项，先获取所有子事项
                if ($copyOptions['copy_children']) {
                    $selectedIssueIds = array_column($issues, 'id');
                    $childIssues = $this->getAllChildIssues($selectedIssueIds);

                    if (!empty($childIssues)) {
                        // 过滤掉已经在用户勾选列表中的子事项，避免重复
                        $filteredChildIssues = array_filter($childIssues, function($childIssue) use ($selectedIssueIds) {
                            return !in_array($childIssue['id'], $selectedIssueIds);
                        });

                        // 记录去重信息
                        $originalChildCount = count($childIssues);
                        $filteredChildCount = count($filteredChildIssues);
                        if ($originalChildCount > $filteredChildCount) {
                            Log::get('system')->info('子事项去重处理', [
                                'original_child_count' => $originalChildCount,
                                'filtered_child_count' => $filteredChildCount,
                                'removed_duplicates' => $originalChildCount - $filteredChildCount,
                                'selected_issue_ids' => $selectedIssueIds
                            ]);
                        }

                        if (!empty($filteredChildIssues)) {
                            // 获取子事项的关联数据
                            $childIssueIds = array_column($filteredChildIssues, 'id');
                            $childCustomFields = $this->getIssuesCustomFields($childIssueIds);
                            $childWatchers = $this->getIssuesWatchers($childIssueIds);
                            $childAssignedUsers = $this->getIssuesAssignedUsers($childIssueIds);

                            // 获取父事项的日期信息用于同步
                            $parentDates = $this->getParentDatesForChildIssues($issues, $filteredChildIssues);

                            // 将子事项转换为与主事项相同的格式
                            $childIssuesFormatted = [];
                            foreach ($filteredChildIssues as $childIssue) {
                                $issueId = $childIssue['id'];
                                $parentId = $childIssue['parent_id'];

                                // 获取对应父事项的日期，如果没有则使用第一个父事项的日期
                                $parentDate = $parentDates[$parentId] ?? reset($parentDates);

                                $childIssuesFormatted[] = [
                                    'id' => $issueId,
                                    'subject' => $childIssue['subject'],
                                    'tracker_id' => $childIssue['tracker_id'],
                                    'status_id' => \App\Constants\IssueCode::ISSUE_STATUS_CREATE_NEW, // 设置为新建状态
                                    'priority_id' => $childIssue['priority_id'],
                                    'category_id' => $childIssue['category_id'] ?? 0,
                                    'fixed_version_id' => $parentDate['fixed_version_id'],
                                    'assigned_to_id' => $childIssue['assigned_to_id'],
                                    'start_date' => $parentDate['start_date'] ?? null, // 同步父事项的开始日期
                                    'due_date' => $parentDate['due_date'] ?? null,     // 同步父事项的完成日期
                                    'class_id' => $childIssue['class_id'] ?? 0,
                                    'parent_id' => $childIssue['parent_id'],
                                    // 添加关联数据
                                    'custom_fields' => $childCustomFields[$issueId] ?? [],
                                    'watchers' => $childWatchers[$issueId] ?? [],
                                    'assigned_to_ids' => $childAssignedUsers[$issueId] ?? []
                                ];
                            }

                            // 将子事项添加到复制列表中
                            $allIssuesToCopy = array_merge($allIssuesToCopy, $childIssuesFormatted);
                        }
                    }
                }

                // 第一轮：复制所有事项（包括子事项，但不处理父子关系）
                foreach ($allIssuesToCopy as $issueData) {
                    try {
                        $originalIssue = $this->model::query()->find($issueData['id']);
                        if (!$originalIssue || !($originalIssue instanceof IssueModel)) {
                            $failedIssues[] = [
                                'id' => $issueData['id'],
                                'subject' => $issueData['subject'] ?? '未知',
                                'error' => '原事项不存在'
                            ];
                            continue;
                        }

                        $newIssue = $this->copyIssue($originalIssue, $targetProjectId, $issueData, $copyOptions);
                        $copiedIssues[] = $newIssue;
                        $originalToNewMapping[$issueData['id']] = $newIssue->id;

                    } catch (\Exception $e) {
                        $failedIssues[] = [
                            'id' => $issueData['id'],
                            'subject' => $issueData['subject'] ?? '未知',
                            'error' => $e->getMessage()
                        ];
                        // 记录错误但继续处理其他事项
                        Log::get('system')->error('复制事项失败', [
                            'issue_id' => $issueData['id'],
                            'error' => $e->getMessage()
                        ]);
                    }
                }

                // 第二轮：处理父子关系（处理所有复制的事项，包括子事项）
                if ($copyOptions['copy_children']) {
                    foreach ($allIssuesToCopy as $issueData) {
                        if (!empty($issueData['parent_id']) && isset($originalToNewMapping[$issueData['parent_id']])) {
                            $newIssueId = $originalToNewMapping[$issueData['id']];
                            $newParentId = $originalToNewMapping[$issueData['parent_id']];

                            try {
                                // 更新父子关系
                                $this->model::query()->where('id', $newIssueId)->update(['parent_id' => $newParentId]);
                            } catch (\Exception $e) {
                                Log::get('system')->error('更新父子关系失败', [
                                    'new_issue_id' => $newIssueId,
                                    'new_parent_id' => $newParentId,
                                    'error' => $e->getMessage()
                                ]);
                            }
                        }
                    }
                }

                // 第三轮：建立原事项与复制事项的关联关系
                $relationErrors = [];
                if ($copyOptions['link_to_original']) {
                    foreach ($originalToNewMapping as $originalId => $newId) {
                        $relationId = $this->createIssueRelation($originalId, $newId, 'relates');
                        if (!$relationId) {
                            $relationErrors[] = [
                                'original_id' => $originalId,
                                'new_id' => $newId
                            ];
                        }
                    }
                }

                DB::connection('tchip_redmine')->commit();

                // 计算原始事项和子事项的数量
                $originalIssueCount = count($issues);
                $totalIssuesToCopy = count($allIssuesToCopy);
                $childIssueCount = $totalIssuesToCopy - $originalIssueCount;

                $result = [
                    'success' => true,
                    'message' => '批量复制完成',
                    'copied_count' => count($copiedIssues),
                    'original_issue_count' => $originalIssueCount,
                    'child_issue_count' => $childIssueCount,
                    'copied_issues' => array_map(function($issue) {
                        return [
                            'id' => $issue->id,
                            'subject' => $issue->subject,
                        ];
                    }, $copiedIssues)
                ];

                // 添加失败信息到结果中
                if (!empty($failedIssues)) {
                    $result['failed_issues'] = $failedIssues;
                    $result['failed_count'] = count($failedIssues);
                }

                if (!empty($relationErrors)) {
                    $result['relation_errors'] = $relationErrors;
                    $result['relation_error_count'] = count($relationErrors);
                }

                return $result;

            } catch (\Exception $e) {
                DB::connection('tchip_redmine')->rollBack();
                throw $e;
            }
        }

        /**
         * 复制单个事项
         * @param IssueModel $originalIssue 原事项
         * @param int $targetProjectId 目标项目ID
         * @param array $issueData 事项数据
         * @param array $copyOptions 复制选项
         * @return IssueModel
         */
        private function copyIssue(IssueModel $originalIssue, $targetProjectId, $issueData, $copyOptions)
        {
            // 准备新事项数据
            $newIssueData = [
                'project_id' => $targetProjectId,
                'tracker_id' => $issueData['tracker_id'],
                'subject' => $issueData['subject'],
                'status_id' => $issueData['status_id'],
                'parent_id' => $issueData['parent_id'] ?? null,
                'priority_id' => $issueData['priority_id'],
                'category_id' => $issueData['category_id'] ?? 0,
                'fixed_version_id' => $issueData['fixed_version_id'],
                'assigned_to_id' => $issueData['assigned_to_id'],
                'start_date' => $issueData['start_date'],
                'due_date' => $issueData['due_date'],
                'class_id' => $issueData['class_id'] ?? 0,
                'author_id' => getRedmineUserId(),
                'created_on' =>  date('Y-m-d H:i:s'),
                'updated_on' => date('Y-m-d H:i:s'),
            ];

            // 如果需要复制描述
            if ($copyOptions['copy_description']) {
                $newIssueData['description'] = $originalIssue->description;
            }


            // 创建新事项
            $newIssue = $this->model::create($newIssueData);

            // 复制附件
            if ($copyOptions['copy_attachments']) {
                $this->copyIssueAttachments($originalIssue->id, $newIssue->id);
            }

            // 复制自定义字段
            if (!empty($issueData['custom_fields'])) {
                $this->copyIssueCustomFields($newIssue->id, $issueData['custom_fields']);
            }

            // 复制关注人
            if (!empty($issueData['watchers'])) {
                $this->copyIssueWatchers($newIssue->id, $issueData['watchers']);
            }

            // 处理多人指派
            if (!empty($issueData['assigned_to_ids'])) {
                $this->updateIssueAssignedUsers($newIssue->id, $issueData['assigned_to_ids']);
            }

            // 复制检查清单
            if ($copyOptions['copy_checklists']) {
                $this->copyIssueChecklists($originalIssue->id, $newIssue->id, $targetProjectId);
            }

            return $newIssue;
        }

        /**
         * 复制事项附件
         * @param int $originalIssueId 原事项ID
         * @param int $newIssueId 新事项ID
         */
        private function copyIssueAttachments($originalIssueId, $newIssueId)
        {
            $attachments = \App\Model\Redmine\AttachmentModel::query()
                ->where('container_type', 'Issue')
                ->where('container_id', $originalIssueId)
                ->get();

            if ($attachments->isEmpty()) {
                return;
            }

            $attachmentData = [];
            foreach ($attachments as $attachment) {
                $attachmentData[] = [
                    'container_id' => $newIssueId,
                    'container_type' => 'Issue',
                    'filename' => $attachment->filename,
                    'disk_filename' => $attachment->disk_filename,
                    'filesize' => $attachment->filesize,
                    'content_type' => $attachment->content_type,
                    'digest' => $attachment->digest,
                    'downloads' => 0,
                    'author_id' => getRedmineUserId(),
                    'created_on' => date('Y-m-d H:i:s'),
                    'description' => $attachment->description,
                    'disk_directory' => $attachment->disk_directory,
                ];
            }

            // 批量插入附件记录
            DB::connection('tchip_redmine')->table('attachments')->insert($attachmentData);
        }

        /**
         * 复制事项自定义字段
         * @param int $newIssueId 新事项ID
         * @param array $customFields 自定义字段数据
         */
        private function copyIssueCustomFields($newIssueId, $customFields)
        {
            if (empty($customFields)) {
                return;
            }

            $customValueData = [];
            foreach ($customFields as $field) {
                if (isset($field['id']) && isset($field['value']) && $field['value'] !== '') {
                    // 处理多值字段
                    if (is_array($field['value'])) {
                        foreach ($field['value'] as $value) {
                            $customValueData[] = [
                                'customized_type' => 'Issue',
                                'customized_id' => $newIssueId,
                                'custom_field_id' => $field['id'],
                                'value' => $value,
                            ];
                        }
                    } else {
                        $customValueData[] = [
                            'customized_type' => 'Issue',
                            'customized_id' => $newIssueId,
                            'custom_field_id' => $field['id'],
                            'value' => $field['value'],
                        ];
                    }
                }
            }

            if (!empty($customValueData)) {
                DB::connection('tchip_redmine')->table('custom_values')->insert($customValueData);
            }
        }

        /**
         * 复制事项关注人
         * @param int $newIssueId 新事项ID
         * @param array $watchers 关注人ID数组
         */
        private function copyIssueWatchers($newIssueId, $watchers)
        {
            if (empty($watchers)) {
                return;
            }

            make(\App\Core\Services\Project\WatcherService::class)->updateWatchers($newIssueId, $watchers, 'Issue');
        }

        /**
         * 复制事项检查清单
         * @param int $originalIssueId 原事项ID
         * @param int $newIssueId 新事项ID
         * @param int $targetProjectId 目标项目ID
         */
        private function copyIssueChecklists($originalIssueId, $newIssueId, $targetProjectId)
        {
            try {
                // 使用CheckListService的批量复制方法，默认重置检查项状态为未完成
                // 由于外部已有事务，这里不使用内部事务
                $result = $this->checkListService->copyChecklistsToIssue(
                    $originalIssueId,
                    $newIssueId,
                    $targetProjectId,
                    true,
                    false
                );

                // 记录复制结果
                if ($result['success'] && $result['copied_count'] > 0) {
                    Log::get('system')->info('检查清单复制成功', [
                        'original_issue_id' => $originalIssueId,
                        'new_issue_id' => $newIssueId,
                        'copied_count' => $result['copied_count']
                    ]);

                    // 验证复制完整性
                    $validation = $this->checkListService->validateChecklistCopy($originalIssueId, $newIssueId);
                    if (!$validation['is_valid']) {
                        Log::get('system')->warning('检查清单复制验证失败', [
                            'original_issue_id' => $originalIssueId,
                            'new_issue_id' => $newIssueId,
                            'validation' => $validation
                        ]);
                    }
                }

            } catch (\Exception $e) {
                // 记录错误但不中断整个复制流程
                Log::get('system')->error('复制检查清单失败', [
                    'original_issue_id' => $originalIssueId,
                    'new_issue_id' => $newIssueId,
                    'error' => $e->getMessage()
                ]);

                // 可以选择抛出异常或继续执行
                // throw new \Exception('复制检查清单失败: ' . $e->getMessage());
            }
        }



        /**
         * 更新事项多人指派
         * @param int $issueId 事项ID
         * @param array $assignedUserIds 指派用户ID数组
         */
        private function updateIssueAssignedUsers($issueId, $assignedUserIds)
        {
            if (empty($assignedUserIds)) {
                return;
            }

            // 先删除现有的指派关系
            \App\Model\Redmine\IssueAssignedModel::query()->where('issue_id', $issueId)->delete();

            // 插入新的指派关系
            $assignedData = [];
            foreach ($assignedUserIds as $userId) {
                $assignedData[] = [
                    'issue_id' => $issueId,
                    'user_id' => $userId,
                    'created_at' =>  date('Y-m-d H:i:s'),
                    'updated_at' =>  date('Y-m-d H:i:s'),
                ];
            }

            if (!empty($assignedData)) {
                DB::connection('tchip_redmine')->table('issue_assigned')->insert($assignedData);
            }
        }

        /**
         * 创建事项关联关系
         * @param int $issueFromId 源事项ID
         * @param int $issueToId 目标事项ID
         * @param string $relationType 关联类型
         * @return int|null 返回创建的关联关系ID，如果已存在或创建失败则返回null
         */
        private function createIssueRelation($issueFromId, $issueToId, $relationType = 'relates')
        {
            try {
                // 检查关联关系是否已存在
                $existingRelation = \App\Model\Redmine\IssueRelationModel::query()
                    ->where('issue_from_id', $issueFromId)
                    ->where('issue_to_id', $issueToId)
                    ->where('relation_type', $relationType)
                    ->first();

                if ($existingRelation) {
                    return null; // 已存在，不需要创建
                }

                // 准备关联数据，只包含表中实际存在的字段
                $relationData = [
                    'issue_from_id' => $issueFromId,
                    'issue_to_id' => $issueToId,
                    'relation_type' => $relationType,
                ];

                // 使用模型创建，现在已经禁用了 timestamps
                $relation = \App\Model\Redmine\IssueRelationModel::create($relationData);
                $relationId = $relation->id;

                return $relationId;

            } catch (\Exception $e) {
                // 记录错误日志但不抛出异常，避免影响整个批量复制流程
                Log::get('system')->error('创建事项关联关系失败', [
                    'issue_from_id' => $issueFromId,
                    'issue_to_id' => $issueToId,
                    'relation_type' => $relationType,
                    'error' => $e->getMessage()
                ]);
                return null;
            }
        }

        /**
         * 解除子项与父项的关联关系
         * @param int $childId 子项ID
         */
        private function unlinkParentChild($childId)
        {
            $issue = $this->model::query()->find($childId);
            if (!$issue || !$issue->parent_id) {
                return;
            }

            $oldParentId = $issue->parent_id;
            $issue->parent_id = null;
            $issue->save();


            $remainingChildren = $this->model::query()
                ->where('parent_id', $oldParentId)
                ->where('id', '!=', $childId)
                ->count();

            // 获取当前时间
            $currentDateTime = ApplicationContext::getContainer()->get(Carbon::class)->now();
            // 设置时区与redmine同步
            $currentDateTime = $currentDateTime->setTimezone('UTC');

            if ($remainingChildren == 0) {
                // 为父事项添加孤立记录
                $parentJournal = new \App\Model\Redmine\JournalsModel();
                $parentJournal->journalized_id = $oldParentId;
                $parentJournal->journalized_type = 'Issue';
                $parentJournal->user_id = getRedmineUserId();
                $parentJournal->notes = "父事项因最后一个子项(#{$childId})被迁移而变为孤立状态";
                $parentJournal->created_on = $currentDateTime;
                $parentJournal->private_notes = 0;
                $parentJournal->save();
            }


            // 插入到 journals 表中的数据
            $newJournal = new \App\Model\Redmine\JournalsModel();
            $newJournal->journalized_id = $issue->id;
            $newJournal->journalized_type = 'Issue';
            $newJournal->user_id = getRedmineUserId();
            $newJournal->notes = '';
            $newJournal->created_on = $currentDateTime;
            $newJournal->private_notes = 0;
            $newJournal->save();

            //插入父项变更记录到 journal_details 表
            $newJournalDetail = new \App\Model\Redmine\JournalDetailsModel();
            $newJournalDetail->journal_id = $newJournal->id;
            $newJournalDetail->property = 'attr';
            $newJournalDetail->prop_key = 'parent_id';
            $newJournalDetail->old_value = $oldParentId;
            $newJournalDetail->value = null;
            $newJournalDetail->save();
        }


        /**
         * 批量迁移事项至其他项目（保留父子关系）
         * @param int $id 事项ID
         * @param int $projectId 目标项目ID
         * @param int|null $versionId 目标版本ID（可选）
         * @param array $allMigratingIds 所有正在迁移的事项ID数组（用于检查父子关系）
         */
        private function migrateIssueProject($id, $projectId, $versionId = null, $allMigratingIds = []) 
        {
            $query = $this->model::query()->find($id);
            if (!$query) {
                return;
            }

            $oldProjectId = $query->project_id;
            $oldVersionId = $query->fixed_version_id;
            $oldParentId = $query->parent_id;
            
            // 检查是否需要清空parent_id
            // 如果当前事项有父项，但父项不在迁移列表中，则清空parent_id
            if ($query->parent_id && !empty($allMigratingIds) && !in_array($query->parent_id, $allMigratingIds)) {
                $query->parent_id = null;
            }
            
            // 更新项目ID
            $query->project_id = $projectId;
            
            // 如果指定了版本ID，则设置版本
            if ($versionId !== null) {
                $query->fixed_version_id = $versionId;
            }else{
                $query->fixed_version_id = null;
            }
            
            $query->save();

            // 获取当前时间
            $currentDateTime = ApplicationContext::getContainer()->get(Carbon::class)->now();
            // 设置时区与redmine同步
            $currentDateTime = $currentDateTime->setTimezone('UTC');

            // 插入到 journals 表中的数据
            $newJournal = new \App\Model\Redmine\JournalsModel();
            $newJournal->journalized_id = $query->id;
            $newJournal->journalized_type = 'Issue';
            $newJournal->user_id = getRedmineUserId();
            $newJournal->notes = '';
            $newJournal->created_on = $currentDateTime;
            $newJournal->private_notes = 0;
            $newJournal->save();

            // 插入项目变更记录到 journal_details 表
            $newJournalDetail = new \App\Model\Redmine\JournalDetailsModel();
            $newJournalDetail->journal_id = $newJournal->id;
            $newJournalDetail->property = 'attr';
            $newJournalDetail->prop_key = 'project_id';
            $newJournalDetail->old_value = $oldProjectId;
            $newJournalDetail->value = $projectId;
            $newJournalDetail->save();

            // 如果版本发生了变化，也记录版本变更
            if ($versionId !== null && $oldVersionId != $versionId) {
                $versionJournalDetail = new \App\Model\Redmine\JournalDetailsModel();
                $versionJournalDetail->journal_id = $newJournal->id;
                $versionJournalDetail->property = 'attr';
                $versionJournalDetail->prop_key = 'fixed_version_id';
                $versionJournalDetail->old_value = $oldVersionId;
                $versionJournalDetail->value = $versionId;
                $versionJournalDetail->save();
            }
            
            // 如果父项ID被清空，也记录父项变更
            if ($oldParentId && !$query->parent_id) {
                $parentJournalDetail = new \App\Model\Redmine\JournalDetailsModel();
                $parentJournalDetail->journal_id = $newJournal->id;
                $parentJournalDetail->property = 'attr';
                $parentJournalDetail->prop_key = 'parent_id';
                $parentJournalDetail->old_value = $oldParentId;
                $parentJournalDetail->value = null;
                $parentJournalDetail->save();
            }
        }


//        public function getChildId(int $id)
//        {
//            $ids = $this->getNextChild($id);
//            foreach ($ids as $i) {
//                $ids = array_merge($ids, $this->getNextChild($i));
//            }
//            return $ids;
//        }
//
//        public function getNextChild(int $id)
//        {
//            $child = $this->model::query()
//                ->select('id')
//                ->where('parent_id', $id)
//                ->get();
//            $ids = $child ? $child->toArray() : [];
//            return array_column($ids, 'id');
//        }

        public function getChildId(int $id, &$ids)
        {
            $child = $this->model::query()
                ->select('id')
                ->where('parent_id', $id)
                ->get();
            $childIds = $child ? $child->toArray() : [];
            $childIds = array_column($childIds, 'id');
            if ($childIds) {
                foreach ($childIds as $i) {
                    $this->getChildId($i, $ids);
                }
                $ids = array_merge($ids, $childIds);
            }

        }


        public function editTask($id, $values, $apiKey) {

            $url = "issues/${id}.json";
            $option = [
                'json' => [
                    'issue' => $values
                ]
            ];
            $this->redmineIssueService->sendRequest($url, $option, 'put', [], $apiKey);
//        $this->redmineIssueService->updateIssue($id, $values);
        }


        public function doEdit(int $id, array $values, $needNotice = true)
        {
            // throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
            if (isset($values['parent_id'])) {
                if (empty($values['parent_id'])) {
                    $values['parent_issue_id'] = null;
                } else {
                    $values['parent_issue_id'] = $values['parent_id'];
                }
            }

            if (isset($values['due_date']) && $values['due_date'] === '') {
                $values['due_date'] = null;
            }

            $ext = null;
            if (!empty($values['issue_ext'])) {
                $ext = $values['issue_ext'];
                unset($values['issue_ext']);
            }

            // 关注人员
            $watchers = null;
            if (isset($values['watchers'])) {
                $watchers = $values['watchers'];
                unset($values['watchers']);
            }

            // 多指派人 设置为空数组之时 清空assigned_to_id
            if (isset($values['assigned']) && is_array($values['assigned']) && count($values) < 2 && count($values['assigned']) == 0) {
                $this->redmineIssueService->updateIssue($id, ['assigned_to_id' => '']);
            }

            $assigned = null;
            if (isset($values['assigned'])) {
                $assigned = $values['assigned'];
                unset($values['assigned']);
            } else {
                if (!empty($values['assigned_to_id'])) {
                    $assigned = [$values['assigned_to_id']];
                }
            }

            // 前端提交的html转换成markdown再保存
            // if (!empty($values['description'])) {
            //     $converter = make(HtmlConverter::class);
            //     $values['description'] = $converter->convert($values['description']);
            // }
            $description = $values['description'] ?? '';

            if ($id > 0) {
                $row = $this->model::query()->with(['customValues' => function ($query) {
                    return $query->where('customized_type', 'Issue');
                }])->find($id);
                $rowArray = $row->toArray();
                if (!$row) {
                    throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                }
                $projectExt = ProjectsExtModel::query()->where('project_id', $row->project_id)->first();
                // 修改版本时，1. 同步更新完成时间; 2. 需要同步修改子事项;
                $fixedVersion = false;
                // 检测版本是否跟随
                if (!empty($projectExt['issue_version_follow']) && $projectExt['issue_version_follow'] == 1) {
                    if (isset($values['fixed_version_id'])) {
                        $fixedVersion = true;
                        if ($values['fixed_version_id'] === 0 || $values['fixed_version_id'] === '0') {
                            $values['fixed_version_id'] = null;
                        }
                    }
                }

                $categoryEdit = false;
                // 获取下级子事项一并修改版本
                if (isset($values['category_id'])) {
                    $categoryEdit = true;
                    if ($values['category_id'] === 0 || $values['category_id'] === '0') {
                        $values['category_id'] = null;
                    }
                }
                // 获取下级子事项一并修改版本
                if (($fixedVersion || $categoryEdit) && count($values) < 5) {
                    $ids = [];
                    $this->getChildId($id,  $ids);
                    if ($ids) {
                        $apiKey =  getRedmineApikey();
                        foreach ($ids as $i) {
                            co(function () use ($i, $values, $apiKey) {
                                $this->editTask($i, $values, $apiKey);
                            });
                        }
                    }
                }

                // 连续更新时判断fixed_version_id
                if (empty($values['lock_version']) || $values['lock_version'] < $row->lock_version) {
                    $values['lock_version'] = $row->lock_version;
                }

//            // 处理多人指派参数赋值
//            $rowArray = $row->toArray();
//            if (isset($values['custom_fields_multi_assign_id'])) {
//                if (empty($values['custom_fields'])) {
//                    $customField = make(CustomFieldsService::class)->getProjectCustomFieldList($rowArray['project_id'], $rowArray['tracker_id']);
//                    $values['custom_fields'] = $this->setIssueCustomFields($id, $customField, $rowArray['custom_values']);
//                }
//                foreach ($values['custom_fields'] as &$custom) {
//                    if ($custom['id'] == IssueCode::MULTI_ASSIGN_ID) {
//                        $custom['value'] = $values['custom_fields_multi_assign_id'];
//                        unset($values['custom_fields_multi_assign_id']);
//                        break;
//                    }
//                }
//            }

                // 处理图片(如有redmine保存的图片，需要还原，原来的值)
                if (!empty($values['description'])) {
                    $values['description'] = reRedmineContentImg($row->description, $values['description'], 'MARKDOWN');
                }

                $attacModel = make(AttachmentModel::class);
                // 处理原来的附件,需要删除已经绑定的附件才可以成功保存
                if (!empty($values['uploads']) && is_array($values['uploads'])) {
                    foreach ($values['uploads'] as $key => $upload) {
                        if (!empty($upload['token'])) {
                            $up = explode('.', $upload['token']);
                            $attac = $attacModel::query()->find($up[0]);

                            if (!empty($attac->container_id)) {
                                unset($values['uploads'][$key]);
                            }

                            if (!$attac) {
                                // 事项描述 上传图片后生成附件 随即删除附件 点击保存 redmine接口提示错误 文件不能保存 问题
                                unset($values['uploads']);
                            }
                        }
                    }
                }
                if (!empty($values)) {
                    $result = $this->redmineIssueService->updateIssue($id, $values);
                    if (isset($values['class_id']) && ($values['class_id'] !== null || $values['class_id'] === 0)) {
                        $classPid = make(IssueClassModel::class)->where('id', $values['class_id'])->value('parent_id') ?? 0;
                        $row->update(['class_id' => $values['class_id'], 'class_pid' => $classPid]);
                    }
                } else {
                    $result = true;
                }

                // 20250321 节点事项状态修改需要发送对应的通知到对应负责人
                if ($needNotice && $rowArray['tracker_id'] == IssueCode::ISSUE_TRACKER_FLOW && !$rowArray['parent_id']) {
                    $newData = $this->model::query()->with(['customValues' => function ($query) {
                        return $query->where('customized_type', 'Issue');
                    }])->find($id)->toArray();
                    make(FlowService::class)->flowNodeNotice(null, null, $rowArray['project_id'], [
                        'newIssueList' => [$newData],
                        'oldIssueList' => [$rowArray],
                    ]);
                }

            } else {
                if (!empty($values['parent_issue_id']) && $values['parent_issue_id'] > 0 && empty($values['custom_fields'])) {
                    $custFields = CustomValuesModel::query()->where('customized_id', $values['parent_issue_id'])->get();
                    $custFields = $custFields ? $custFields->toArray() : [];
                    foreach ($custFields as $cust) {
                        $values['custom_fields'][] = [
                            'customized_type' => 'Issue',
                            'id' => $cust['custom_field_id'],
                            'value' => $cust['value'],
                        ];
                    }
                }

                // 如果有父级事项，先检查父级事项是否已关闭
                $parentIssue = null;
                $parentStatus = null;
                if (!empty($values['parent_id'])) {
                    $parentIssue = $this->model::query()->find($values['parent_id']);
                    if (!$parentIssue) throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                    if (IssueStatusModel::query()->where('id', $parentIssue->status_id)->value('is_closed') == 1) {
                        $parentStatus = $parentIssue->status_id;
                        $parentIssue->update(['status_id' => 0]);
                    }
                }

                $result = $this->redmineIssueService->createIssue($values);
                $id = $result['issue']['id'] ?? '';
                if ($parentIssue && $parentStatus) {
                    $parentIssue->update(['status_id' => $parentStatus]);
                }
                if (isset($values['class_id']) && ($values['class_id'] !== null || $values['class_id'] === 0)) {
                    $row = $this->model::query()->with(['customValues' => function ($query) {
                        return $query->where('customized_type', 'Issue');
                    }])->find($id);
                    if ($row) {
                        $classPid = make(IssueClassModel::class)->where('id', $values['class_id'])->value('parent_id') ?? 0;
                        $row->update(['class_id' => $values['class_id'], 'class_pid' => $classPid]);
                    }
                }
            }
            // 如果是研发流程的事项，需要更新产品的状态
            $subject = $values['subject'] ?? $row->subject;
            $isEditProductStatus = Context::get('is_edit_product_status');
            if (!empty($values['tracker_id']) && $values['tracker_id'] == IssueCode::ISSUE_TRACKER_FLOW &&
                !empty($values['status_id']) && in_array($values['status_id'], make(IssueStatusesService::class)->getCloseStatusIds()) &&
                in_array($subject, FlowModel::$topNodesName) && empty($isEditProductStatus)
            ) {
                $this->doEditProductStatus($values['project_id'] ?? $row->project_id, $subject);
            }

            // 处理关注人员
            if ($watchers || is_array($watchers)) {
                make(\App\Core\Services\Project\WatcherService::class)->updateWatchers($id, $watchers);
            }
            // 处理指派人
            if ($assigned || is_array($assigned)) {
                if (count($assigned) == 1) {
                    $this->redmineIssueService->updateIssue($id, ['assigned_to_id' => $assigned[0]]);
                }
                make(\App\Core\Services\Project\IssueAssignedService::class)->updateAssigned($id, $assigned);
            }

            if ($id || $result) {
                if ($ext) {
                    IssuesExtModel::updateOrCreate(['issue_id' => $id], $ext);
                }
                if ($description) {
                    make(\App\Core\Services\Notice\NoticeService::class)->atMe($id, $description);
                }
            }

            $id && make(CheckListService::class)->resetCheckListItemStatusByIssueJournal($id);

            return $result;
        }

        /**
         * 保存事项本地版
         * 1. 当事项是父事项时：
         *     - 如果还有未完成的子事项，当前事项不可完成
         *     - 修改版本时，如果子事项随便即需要更新所有子事项(顶级事项条件)
         * 2. 当事项是子事项时:
         *     - 如果所有子事项已经完成，那么可以完成父级事项
         *     - 修改版本时，如果随便即不可以修改
         * @param int $id
         * @param array $values
         * @return void
         */
        public function doEditLocal(int $id = 0, array $values = [])
        {
            // 取出ext数据
            $ext = [];
            if (!empty($values['issue_ext'])) {
                $ext = $values['issue_ext'];
                unset($values['issue_ext']);
            }
            // 取出处理人数据
            $assignedIds = [];
            if (!empty($values['assigned_ids'])) {
                $assignedIds = $values['assigned_ids'];
                unset($values['assigned_ids']);
            }
            // 是否加入原生的处理人数据
            if (empty($values['assigned_to_id']) && !empty($assignedIds[0])) {
                $values['assigned_to_id'] = $assignedIds[0];
            } else if (!empty($values['assigned_to_id'])) {
                array_unique(array_unshift($assignedIds, $values['assigned_to_id']));
            }

            // 关注人员
            $watchers = null;
            if (isset($values['watchers'])) {
                $watchers = $values['watchers'];
                unset($values['watchers']);
            }

            Db::connection(DataBaseCode::TCHIP_REDMINE)->beginTransaction();
            try {
                $closeStatusIds = make(\App\Core\Services\Project\IssueStatusesService::class)->getCloseStatusIds();
                $todoStatusIds = make(\App\Core\Services\Project\IssueStatusesService::class)->getToDoStatusIds();
                $allChilds = $this->getAllChildIssues([$id]);
                if ($id > 0) {
                    // 更新
                    $row = $this->model::query()->find($id);
                    if (!$row) {
                        throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                    }
                    $projectExt = ProjectsExtModel::query()->where('project_id', $row->project_id)->first();
                    $ext['issue_id'] = $id;

                    // 当前为子事项:如果存在父事项，处理关联父事项的逻辑
                    $parent = null;
                    if (!empty($values['parent_id'])) {
                        $parent = $this->model->find($values['parent_id']);
                        // 判断父事项已完成时如果子事项修改的状态不是已完成的
                        if (in_array($parent->status_id, $closeStatusIds) && !in_array($values['status_id'], $closeStatusIds)) {
                            throw new AppException(StatusCode::ERR_SERVER, __('common.Open_issue_to_close_issue'));
                        }
                    }

                    // 当前为父事项:存在子事项时
                    if ($allChilds) {
                        $allChildStatusIds = array_column($allChilds, 'status_id');
                        if (array_diff($allChildStatusIds, $todoStatusIds)) {
                            throw new AppException(StatusCode::ERR_SERVER, __('common.To_do_issue_exist'));
                        }
                    }

                    // 存在父级事项时(即当前为子事项)
                    if ($parent) {
                        // 如果修改版本需要不跟着条件
                        if (isset($values['fixed_version_id']) && $values['fixed_version_id'] != $row->fixed_version_id) {
                            // 子事项不可独自修改版本
                            if (empty($projectExt['issue_version_follow']) || $projectExt['issue_version_follow'] == 0) {
                                unset($values['fixed_version_id']);
                            }
                        }
                    }



                    // 处理图片(如有redmine保存的图片，需要还原，原来的值)
                    if (!empty($values['description'])) {
                        $values['description'] = reRedmineContentImg($row->description, $values['description'], 'MARKDOWN');
                    }

                    $this->model::query()->where('id', $id)->update($values);



                } else {
                    // 创建
                    $result = $this->model::create($values);

                    $ext['issue_id'] = $result->id;
                }

                // 处理保存事项扩展数据
                $this->model->issueExt()->updateOrCreate(['issue_id' => $ext['issue_id']], $ext);
                // 处理保存多个处理人数据
                if ($assignedIds) {
                    make(IssueAssignedService::class)->saveAssigned($ext['issue_id'], $assignedIds, $values['assigned_to_id'] ?? null);
                }
                // 处理关注人员数据
                if ($watchers || is_array($watchers)) {
                    make(\App\Core\Services\Project\WatcherService::class)->updateWatchers($id, $watchers);
                }

                // 处理如果存在父级事项时的逻辑
                if (!empty($parent)) {
                    // 如果所有子事项已经完成那些自动完成父事项
                    if (!empty($values['status_id']) && in_array($values['status_id'], $closeStatusIds)) {

                        $allChildStatusIds = array_column($allChilds, 'status_id');
                        if (!$allChilds || !array_diff($allChildStatusIds, $todoStatusIds)) {
                            $this->doEdit($parent['id'], ['status_id' => $values['status_id']]);
                        }
                    }
                } else {
                    // 顶级事项处理逻辑
                    if ($allChilds) {
                        // 一般情况下子事项版本是跟随父事项的
                        if (isset($values['fixed_version_id']) &&
                            $row->fixed_version_id != $values['fixed_version_id'] &&
                            empty($projectExt['issue_version_follow'])
                        ) {
                            foreach ($allChilds as $child) {
                                $this->doEdit($child['id'], ['fixed_version_id' => $values['fixed_version_id']]);
                            }
                        }
                    }
                }


                Db::connection(DataBaseCode::TCHIP_REDMINE)->commit();
            } catch (\Exception $e) {
                Db::connection(DataBaseCode::TCHIP_REDMINE)->rollBack();
                throw new AppException($e->getMessage());
            }

        }

        public function doDelete($ids): int
        {

            // 获取当前用户ID
            $currentRedmineUserId = getRedmineUserId();
            $row = $this->model::query()->find($ids);

            //判断是否为创建人
            $isAuthor = 0;
            if ($row->author_id == $currentRedmineUserId) {
                $isAuthor = 1;
            }

            //判断是否为项目成员
            $memberService = make(MemberService::class);
            $projectMembers = $memberService->getProjectMembers($row->project_id);

            $isManager = false;
            foreach ($projectMembers as $member) {
                if ($member['user_id'] === $currentRedmineUserId) {
                    $isManager = in_array('Manager', array_column($member['role_info'], 'role_name'), true);
                    if ($isManager) break;
                }
            }

            // 创建人或项目Manager都可以删除
            if (!$isAuthor && !$isManager) {
                throw new AppException(StatusCode::ERR_SERVER, '只有创建人或项目Manager才能删除Issue');
            }

            if ($row) {
                DB::connection('tchip_redmine')->beginTransaction();
                try {
                    $result = $this->model::query()->where('id', $ids)->delete();
                    IssuesExtModel::query()->where('issue_id', $ids)->delete();
                    $journals = JournalsModel::query()->where('journalized_id', $ids)->where('journalized_type', 'Issue')->pluck('id')->toArray();
                    if ($journals) {
                        JournalsModel::query()->whereIn('id', $journals)->delete();
                        JournalExtModel::query()->whereIn('journal_id', $journals)->delete();
                        JournalDetailsModel::query()->whereIn('journal_id', $journals)->delete();
                    }
                    make(\App\Core\Services\Project\FlowService::class)->syncIssueNode($row->toArray());
                    DB::connection('tchip_redmine')->commit();
                } catch (AppException $e) {
                    DB::connection('tchip_redmine')->rollBack();
                    throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
                }
                return 1;
            } else {
                throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
            }
        }

        /**
         * 批量创建项目
         * @param $values
         * @return bool
         */
        public function doMultiCreate($project_id, $tracker_id, $values)
        {
            if (!empty($values['assigned_to_id'])) {
                if($values['assigned_to_id'][0] == null) {
                    $values['assigned_to_id'][0] = "";
                }
            }
            if (!empty($values['subject']) && is_array($values['subject'])) {
                $prev_assigned_to_id = $prev_fixed_version_id = null;
                $multipAssignedField = CustomFieldsModel::query()->find(IssueCode::MULTI_ASSIGN_ID);
                $multipAssignedField = $multipAssignedField ? $multipAssignedField->toArray() : null;
                foreach ($values['subject'] as $key => $subject) {
                    if ($subject) {
                        // 处理多人指派处理
                        if (isset($values['assigned_to_id'][$key]) && is_array($values['assigned_to_id'][$key])) {
                            // 存在多人指派字段
                            if ($multipAssignedField) {
                                $multipAssignedField['value'] = !empty($values['assigned_to_id'][$key]) ? $values['assigned_to_id'][$key] : ($prev_assigned_to_id ? : null);
                                $assigned_to_id = $multipAssignedField;
                            } else {
                                $assigned_to_id = ($values['assigned_to_id'][$key][0] ?? ($prev_assigned_to_id ? : null));
                            }
                        } else {
                            // 处理单人指派处理
                            $assigned_to_id = $values['assigned_to_id'][$key] ?? null;
                            if ($assigned_to_id == 'ditto') {
                                $assigned_to_id = $prev_assigned_to_id;
                            }
                        }

                        $fixed_version_id = $values['fixed_version_id'][$key] ?? null;
                        if ($fixed_version_id == 'ditto' && $prev_fixed_version_id) {
                            $fixed_version_id = $prev_fixed_version_id;
                        }

                        $saveData = [
                            'project_id'       => $project_id,
                            'tracker_id'       => $tracker_id,
                            'subject'          => $subject,
                            // 'assigned_to_id'   => $assigned_to_id,
                            // 'fixed_version_id' => $fixed_version_id,
                            'description'      => $subject,
                            'status_id'        => 1,
                            'priority_id'      => 3,
                            'start_date'       => date('Y-m-d'),
                        ];

                        // 多人指派赋值
                        if (is_array($assigned_to_id)) {
                            $saveData['custom_fields'][] = $assigned_to_id;
                            $prev_assigned_to_id   = $assigned_to_id['value'];
                        } else if ($assigned_to_id){
                            // 单人指派
                            $saveData['assigned_to_id'] = $assigned_to_id;
                            $prev_assigned_to_id   = $assigned_to_id;
                        }
                        if (!empty($fixed_version_id) && $fixed_version_id > 0) {
                            $saveData['fixed_version_id'] = $fixed_version_id;
                        }
                        if (!empty($values['start_date'])) {
                            $saveData['start_date'] = $values['start_date'][$key];
                        }
                        if (!empty($values['due_date'])) {
                            $saveData['due_date'] = $values['due_date'][$key];
                        }

                        // 如果有父级事项，先检查父级事项是否已关闭
                        $parentIssue = null;
                        $parentStatus = null;
                        if (!empty($saveData['parent_id'])) {
                            $parentIssue = $this->model::query()->find($saveData['parent_id']);
                            if (!$parentIssue) throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                            if (IssueStatusModel::query()->where('id', $parentIssue->status_id)->value('is_closed') == 1) {
                                $parentStatus = $parentIssue->status_id;
                                $parentIssue->update(['status_id' => 0]);
                            }
                        }
                        $this->redmineIssueService->createIssue($saveData);
                        $prev_fixed_version_id = $saveData['fixed_version_id'];
                        if ($parentIssue && $parentStatus) {
                            $parentIssue->update(['status_id' => $parentStatus]);
                        }
                    }
                }
            }
            return true;
        }

        /**
         * 关注任务
         * @return void
         */
        public function doWatchers(int $id, $watcher)
        {
            $redmineUserid = getRedmineUserId();
            if($redmineUserid){
                if($watcher){
                    $result = $this->redmineIssueService->addWatchers($id, $redmineUserid);
                }else{
                    $result = $this->redmineIssueService->delWatchers($id, $redmineUserid);
                }
            }
            return $result;
        }

        /**
         * 关注任务(多个)
         * @return true
         */
        public function doWatchersMulti($ids, $watcher)
        {
            $redmineUserid = getRedmineUserId();
            if($redmineUserid){
                $ids = is_array($ids) ? $ids : explode(',', $ids);
                foreach ($ids as $id) {
                    if($watcher){
                        $this->redmineIssueService->addWatchers($id, $redmineUserid);
                    }else{
                        $this->redmineIssueService->delWatchers($id, $redmineUserid);
                    }
                }
            }
            return true;
        }

        public function doEditProductStatus($projectId, $name)
        {
            $status = null;
            switch ($name) {
                case '产品立项':
                    // 完成产品立项。自动设置为研发中
                    $status = 0;
                    break;
                case '研发':
                    // 完成研发。自动设置为已试产
                    $status = 1;
                    break;
                case '小批量':
                case '大批量':
                    // 完成小批量。自动设置为已量产
                    $status = 2;
                    break;
            }
            if ($status !== null) {
                make(\App\Core\Services\Project\ProjectsInfoService::class)->doEditProjectId($projectId, ['product_status' => $status]);
            }
        }

        public function getIssueStatus()
        {
            return make(\App\Core\Services\Project\IssueStatusesService::class)->getIssueStatus();
        }

        /**
         * 获取权限状态列表
         * @return Builder[]|Collection
         */
        public function getNewIssueStatus($uid = null, $projectId, $trackerId, $oldStatusId = 0, $authorId = 0, $assignId = 0) : array
        {
            $uid     = $uid ?? getRedmineUserId();
            $isSuper = false;
            $user = UserModel::query()->where('id', $uid)->first();
            if ($user->admin == 1) {
                $isSuper = true;
            }

            $where = [
                'workflows.tracker_id'     => $trackerId,
                'workflows.old_status_id'  => $oldStatusId,
                // 'issue_statuses.is_closed' => 0,
                // 'workflows.author'        => 0,
                // 'workflows.assignee'      => 0,
            ];
            // if ($trackerId == IssueCode::ISSUE_TRACKER_FLOW && $oldStatusId == 0) {
            //     $where['workflows.old_status_id'] = IssueCode::ISSUE_STATUS_NOTSTART;
            // }
            if (!$isSuper) {
                if ($uid == $authorId) {
                    $where['workflows.author'] = 1;
                }
                if ($uid == $assignId) {
                    $where['workflows.assignee'] = 1;
                }
            }
            $workStatusList = WorkflowsModel::query()
                ->select(['issue_statuses.id', 'workflows.new_status_id', 'issue_statuses.name', 'issue_statuses.position'])
                ->join('issue_statuses', 'workflows.new_status_id', '=', 'issue_statuses.id')->where($where);
            if ($isSuper) {
                $workStatusList->where('workflows.role_id', make(MemberRolesModel::class)->manager_id);
            } else {
                $roles = MemberModel::query()->join('member_roles', 'members.id', '=', 'member_roles.member_id')
                    ->where(['members.user_id' => $uid, 'members.project_id' => $projectId])->pluck('role_id');
                $roles = $roles ? $roles->toArray() : [0];
                $workStatusList->whereIn('workflows.role_id', $roles);
            }
            $workStatusList = $workStatusList->groupBy('new_status_id')->orderBy('position')->get();
            $workStatusList = $workStatusList ? $workStatusList->toArray() : [];
            if ($oldStatusId) {
                $oldStatus           = IssueStatusModel::query()
                    ->selectRaw("id, id as new_status_id, name, position")->where('id', $oldStatusId)->first();
                $oldStatus           = $oldStatus ? $oldStatus->toArray() : [];
                $oldStatus['select'] = true;
                $workStatusList      = array_merge([$oldStatus], $workStatusList);
            }
            return $workStatusList;
        }

        public function reTrackerIssueStatus($issueId)
        {
            $issue      = IssueModel::query()->where('id', $issueId)->first();
            $statusList = [];
            if ($issue) {
                $statusList = $this->getNewIssueStatus(null, $issue->project_id, $issue->tracker_id, $issue->status_id, $issue->author_id, $issue->assigned_to_id);
            }
            return $statusList;
        }

        /**
         * 格式化任务描述
         * @param $id
         * @param $description
         * @return array|string|string[]
         */
        public function formatIssueDesc($id, $description)
        {
            $description = Parsedown::instance()->text($description);
            // 获取img标签
            preg_match_all('/<img.*?(?:>|\/>)/', $description, $images);
            if (!empty($images[0]) && count($images[0]) > 0) {
                foreach ($images[0] as $ival) {
                    // 获取url
                    preg_match('/src=[\'\"]?([^\'\"]*)[\'\"]?/', $ival, $img);
                    if (!empty($img[1]) && strpos($img[1], 'http') !== 0) {
                        $attUrl         = AttachmentModel::query()->where(['container_id' => $id, 'container_type' => 'Issue', 'filename' => $img[1]])->first();
                        $redmineFileUrl = !empty($attUrl->url) ? $attUrl->url : $img[1];
                        $description    = str_replace($img[1], $redmineFileUrl, $description);
                    }
                }
            }
            return $description;
        }

        /**
         * 获得任务历史记录详情
         * @param $id
         * @return array|mixed[]
         */
        public function issueJournals($id)
        {
            $row = JournalsModel::query()->with(['details', 'userInfo:id,firstname,lastname'])->where('journalized_id', $id)->get();
            $row = $row ? $row->toArray() : [];
            foreach ($row as &$rval) {
                if (!empty($rval['details'])) {
                    foreach ($rval['details'] as &$detail) {
                        $this->getJournalsAttr($detail, $detail['prop_key'], $detail['old_value'], $detail['value']);

                    }
                }
            }
            return $row;
        }

        /**
         * 解析历史记录数据说明
         * @param $detail
         * @param $attr
         * @param $oldValue
         * @param $value
         * @return void
         */
        public function getJournalsAttr(&$detail, $attr, $oldValue, $value)
        {
            $detail['property_name'] = $this->model->fieldName[$attr] ?? '';
            switch ($attr) {
                case 'attachment' :
                    $attac = AttachmentModel::query()->where('id', $value)->first();
                    $detail['value_name'] = $attac ? $attac->url_attr : '';
                    break;
                case  'status_id':
                    $statusList = IssueStatusModel::query()->whereIn('id', [$oldValue, $value])->get();
                    if ($statusList) {
                        foreach ($statusList as $status) {
                            if ($status->id == $oldValue) {
                                $detail['oldValue_name'] = $status->name;
                            }else if($status->id == $value) {
                                $detail['value_name'] = $status->name;
                            }
                        }
                    }
                    break;
                case 'tracker_id':
                    $trackers = TrackersModel::query()->whereIn('id', [$oldValue, $value])->get();
                    if ($trackers) {
                        foreach ($trackers as $tracker) {
                            if ($tracker->id == $oldValue) {
                                $detail['oldValue_name'] = $tracker->name;
                            }else if($tracker->id == $value) {
                                $detail['value_name'] = $tracker->name;
                            }
                        }
                    }
                    break;
                case 'project_id':
                    $projects = ProjectModel::query()->whereIn('id', [$oldValue, $value])->get();
                    if ($projects) {
                        foreach ($projects as $project) {
                            if ($project->id == $oldValue) {
                                $detail['oldValue_name'] = $project->name;
                            }else if($project->id == $value) {
                                $detail['value_name'] = $project->name;
                            }
                        }
                    }
                    break;
                default:
                    $detail['value_name'] = $value;
            }
        }

        /**
         * 批量编辑处理
         * @param $ids
         * @param $values
         * @return bool
         */
        public function doMulti($ids, $values) : bool
        {
            if (isset($values['class_id']) && ($values['class_id'] !== null || $values['class_id'] === 0)) {
                $classPid = make(IssueClassModel::class)->where('id', $values['class_id'])->value('parent_id') ?? 0;
                $values['class_pid'] = $classPid;
                IssueModel::whereIn('id', explode(',', $ids))->update([
                    'class_id' => $values['class_id'],
                    'class_pid' => $values['class_pid'],
                ]);
                return true;
            }
            if (isset($values['assigned_to_id']) && $values['assigned_to_id'] == -1) {
                $values['assigned_to_id'] = "";
            }
            if (!is_array($ids)) {
                $ids = explode(',', $ids);
            }

            $assigned = [];
            if ( isset($values['assigned_to_id']) && is_array($values['assigned_to_id'])) {
                $assigned = $values['assigned_to_id'];
                if (count($values['assigned_to_id']) <= 0) {
                    $values['assigned_to_id'] = '';
                } else {
                    $values['assigned_to_id'] = $assigned[0];
                }
            }

            // 批量操作事项父任务属性时暂不使用协程，批量解除父任务时（写入空字符''）会引发redmine数据库死锁
            if ((isset($values['parent_id']) && $values['parent_id'] === '') || !empty($values['parent_id'])) {
                $values['parent_issue_id'] = $values['parent_id'];
                $parentIssue = $this->model::query()->find($values['parent_issue_id']);
                if ($parentIssue) {
                    // 同步模块
                    if (!empty($parentIssue->category_id)) {
                        $values['category_id'] = $parentIssue->category_id;
                    }
                }
                unset($values['parent_id']);
            }
            $errorMessages = '';

            // apikey，在协程内使用
            $apiKey = TokenModel::query()->where('user_id', getRedmineUserId())->where('action', 'api')->value('value');
            // 加入10条协程帮助处理请求
            // 20240625 增加到30条 因由部分能改部分不能改,故尝试此操作
            $parallel = new Parallel(30);
            foreach ($ids as $id) {
                if (isset($values['parent_issue_id']) && $values['parent_issue_id'] > 0) {
                    if ($values['parent_issue_id'] == $id) {
                        throw new AppException(StatusCode::ERR_SERVER, __('tchipredmine.Cannot_set_self_parent'));
                    }
                    $chilIssueCount = $this->model::query()->where('parent_id', $id)->count();
                    if ($chilIssueCount && $chilIssueCount > 0) {
                        throw new AppException(StatusCode::ERR_SERVER, __('tchipredmine.Edit_parent_issue_child_exsits'));
                    }
                }
                $parallel->add(function () use ($id, $values, $apiKey, &$errorMessages, $assigned) {
                    try {
                        make(\App\Core\Services\Redmine\IssueService::class)->sendRequest("issues/${id}.json", [
                            'json' => [
                                'issue' => $values
                            ]
                        ], 'put', [], $apiKey);

                        if (isset($values['assigned_to_id'])) {
                            make(\App\Core\Services\Project\IssueAssignedService::class)->updateAssigned($id, $assigned);
                        }

                        return Coroutine::id();
                    } catch (\Exception $e) {
                        $errorMessages .= '#' . $id.'事项修改失败 ' . $e->getMessage(). ' <br><br> ';
                        throw new AppException(StatusCode::ERR_SERVER, __('tchipredmine.Update_issue_failed', ['id' => $id, 'error' => $e->getMessage()]));
                    }
                });
            }
            try {
                $parallel->wait();
            } catch (\Exception $e) {
                throw new AppException(StatusCode::ERR_SERVER, __($errorMessages));
            }

            return true;
        }

        /**
         * 统计 - 获取用户未完成任务数量
         * @param $redmineUserId
         * @return int
         */
        public function toDoCount($redmineUserId = null, $isSendEmail = false)
        {
            $redmineUserId = $redmineUserId ?? getRedmineUserId();
            $count = 0;
            if ($redmineUserId) {
                $statusTodoList = $this->model->status_todo;
                if ($isSendEmail) {
                    // 依据sendNoticeJob.php中调用getToDoStatusIds()的返回内容
                    $statusTodoList = [1, 2, 6, 7, 8, 9, 10];
                }

                $query = $this->model->newQuery()
                    ->whereIn('status_id', $statusTodoList);

                // 使用 whereExists 定义子查询
                $query->where(function ($query) use ($redmineUserId) {
                    $query->whereExists(function ($subQuery) use ( $redmineUserId) {
                        $subQuery->selectRaw(1)
                            ->from('issue_assigned')
                            ->whereColumn('issue_assigned.issue_id', 'issues.id')
                            ->where('issue_assigned.user_id', $redmineUserId)
                            ->whereNull('issue_assigned.deleted_at');
                    });
                });

                $count = $query->count();
            }
            return $count;
        }

        /**
         * 统计 - 获取用户未完成任务数量
         * @param $redmineUserId
         * @return int
         */
        public function overdueCount($redmineUserId = null)
        {
            $redmineUserId = $redmineUserId ?? getRedmineUserId();
            $count = 0;
            if ($redmineUserId) {
                $count = $this->model::query()->whereIn('status_id', $this->model->status_todo)
                    ->where('due_date', '<', date('Y-m-d'))
                     // 使用 whereExists 定义子查询
                    ->where(function ($query) use ($redmineUserId) {
                    $query->whereExists(function ($subQuery) use ( $redmineUserId) {
                        $subQuery->selectRaw(1)
                            ->from('issue_assigned')
                            ->whereColumn('issue_assigned.issue_id', 'issues.id')
                            ->where('issue_assigned.user_id', $redmineUserId)
                            ->whereNull('issue_assigned.deleted_at');
                    });
                })->count();
                    //->where('assigned_to_id', $redmineUserId)->count();
            }
            return $count;
        }

        /**
         * 获取用户关注任务数量
         * @param $redmineUserId
         * @return void
         */
        public function watcherCount($redmineUserId = null)
        {
            $redmineUserId = $redmineUserId ?? getRedmineUserId();
            $count = 0;
            if ($redmineUserId) {
                $count = WatchersModel::query()->where('watchable_type', 'Issue')->where('user_id', $redmineUserId)->count();
            }
            return $count;
        }

        public function userCreateCount($redmineUserId = null)
        {
            $redmineUserId = $redmineUserId ?? getRedmineUserId();
            $count = 0;
            if ($redmineUserId) {
                $count = $this->model->where('author_id', $redmineUserId)->count();
            }
            return $count;
        }

        /**
         * 统计事项数量
         * @param $filter
         * @param $op
         * @return int
         */
        public function issueCount($filter, $op = []): int
        {
            if (!empty($filter['status_id'])) {
                if ($filter['status_id'] == 'todo') {
                    $filter['status_id'] = implode(',', $this->model->status_todo);
                    $op['status_id'] = 'IN';
                }
            }
            $query = $this->model::query();
            /* @var $query IssueModel*/
            list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, 'id', 'desc', 1, $query);
            $result = $query->count();
            return $result ? : 0;
        }

        /**
         * 自定义属性
         * @param $issueId
         * @param array $customFields
         * @param array $customValues
         * @return array
         */
        public function setIssueCustomFields($issueId, array $customFields, array $customValues)
        {
            $result = [];
            foreach ($customFields as $key => $field) {
                $values = $field['multiple'] == 1 ? [] : '';
                foreach ($customValues as $value) {
                    if ($field['id'] == $value['custom_field_id']) {
                        if (!empty($value['value'])) {
                            if (is_array($values)) {
                                $values[] = is_numeric($value['value']) ? (int) $value['value'] : $value['value'];
                            } else {
                                $values = $field['field_format'] == 'user' ? (int) $value['value'] : $value['value'];
                            }
                        }

                    }
                }
                $result[$key] = [
                    'value' => $values,
                    'field' => $field,
                    'customized_type' => 'Issue',
                    'customized_id' => $issueId,
                    'id' => $field['id'],
                    'name' => $field['name'],
                ];
            }
            return $result;
        }

        /**
         * 同步事项处理人到多人指派
         */
        public function assignToIdMigrations()
        {
            Log::get('system')->info('开始同步指派人到多人属性中');
            $issues = $this->model::query()->whereNotNull('assigned_to_id')->orderBy('id', 'asc')->get();
            $count = count($issues);
            $customValueModel = make(CustomValuesModel::class);
            foreach ($issues as $key => $issue) {
                $customValueModel::query()->firstOrCreate(
                    ['customized_type' => 'Issue', 'customized_id' => $issue->id, 'custom_field_id' => IssueCode::MULTI_ASSIGN_ID, 'value' => $issue->assigned_to_id],
                    ['value' => $issue->assigned_to_id]
                );
                $now = $key + 1;
                Log::get('system')->info("完成第{$now}个,issueId:{$issue->id}.总数({$now}/$count)");
            }
            return true;
        }

        public function getToDoStatusList() :array
        {
            $model = make(\App\Model\Redmine\IssueStatusModel::class);
            $list = $model::query()->where('is_closed', 0)->orderBy('position', 'ASC')->get();
            return $list ? $list->toArray() : [];
        }

        public function getToDoStatusIds() :array
        {
            $list = $this->getToDoStatusList();
            $ids = array_column($list, 'id');
            return $ids;
        }

        /**
         *
         * @param $issueId
         * @return void
         */
        public function getTopIssue($id)
        {
            $row = $this->model::query()->find($id);
            if (!empty($row) && $row->parent_id > 0) {
                return $this->getTopIssue($row->parent_id);
            }
            return $row ? $row->toArray() : [];
        }

        /**
         * 构建通用的list query
         * @param Model $query model
         * @param string $sort 排序字段
         * @param string $order 排序方式
         * @return void
         */
        public function buildIssueListQuery(&$query, string $sort,  string $order, bool $notExitInIssueAssignedTable, array $relations = null)
        {
            //var_dump('buildIssueListQuery');
            $withRealtions = $relations ?? ['categoryText','issueExt', 'attachment', 'issueStatus', 'projectText', 'authorText', 'enumerations', 'issueAssigned'];
            $query = $query->selectRaw('issues.*, IF(parent_id > 0, (select subject from issues s where s.id = issues.parent_id), \'\') as parent_name');
            $query = $query
                ->with($withRealtions)
                ->with('versionText:id,name')
                ->with('issueType:id,name');

            // 根据 sort 的值进行排序逻辑的调整
            if ($sort === 'watch_timing') {
                $userId = getRedmineUserId();
                $query = $query->addSelect([Db::raw("(SELECT id FROM watchers WHERE watchers.watchable_id = issues.id AND watchers.user_id = {$userId} ORDER BY id DESC LIMIT 1) as watch_timing")]);
                // 使用获取到的 latest_watcher_id 进行排序
                $query = $query->orderBy('watch_timing', $order)->orderBy('created_on', 'DESC');
            } else {
                $query = $query->orderBy($sort, $order);
            }

            if ($notExitInIssueAssignedTable) {
                $query = $query->whereNotExists(function ($query) {
                    $query->select(DB::raw(1))
                        ->from('issue_assigned')
                        ->whereRaw('issue_assigned.issue_id = issues.id')
                        ->where('issue_assigned.deleted_at', null);
                });
            }
        }

        /**
         * 同步issue处理人到issue_assigned表
         * @return bool
         */
        public function syncIssueAssigned()
        {
            $issues = $this->model->get();
            foreach ($issues as $issue) {
                if (empty($issue->assigned_to_id)) continue;
                $where = [
                    'issue_id' => $issue->id,
                    'user_id' => $issue->assigned_to_id,
                ];
                $saveData = [
                    'issue_id' => $issue->id,
                    'user_id' => $issue->assigned_to_id,
                    'leader' => 1,
                ];
                \App\Model\Redmine\IssueAssignedModel::firstOrCreate($where, $saveData);
            }
            return true;
        }

        /**
         * 统一处理事项列表附加属性
         * @param $data
         * @return void
         */
        public function formatIssueData(&$data)
        {
            //var_dump('formatIssueData');
            $data = array_map(function($item) {
                // 加入状态列表
                $newStatusList       = $this->getNewIssueStatus(null, $item['project_id'], $item['tracker_id'], $item['status_id'], $item['author_id'], $item['assigned_to_id']);
                $item['status_list'] = $newStatusList;

                // 为单条数据增加priority_position
                if (isset($item['enumerations']['position'])) {
                    $item['priority_position'] = $item['enumerations']['position'];
                }
                // 加入处理人列表
                // $item['assigned'] = !empty($item['issue_assigned']) ? array_column($item['issue_assigned'], 'user_id') : (!empty($item['assigned_to_id']) ? [$item['assigned_to_id']] : []);
                $item['assigned'] = !empty($item['issue_assigned']) ? array_column($item['issue_assigned'], 'user_id') : [];
                return $item;
            }, $data);
        }

        /**
         * 获取参与的项目的版本
         */
        public function getIssueVersions()
        {
            $userId = getRedmineUserId();
            $projectData = make(ProjectService::class)->getJoinedProjectList($userId);

            // 获取 project_id => name 的映射
            $projectMap = collect($projectData['data'])->pluck('name', 'id')->toArray();

            $projectIds = array_keys($projectMap);

            $versions = make(VersionModel::class)
                ->whereIn('project_id', $projectIds)
                ->where('status', 'open')
                ->orderBy('project_id')
                ->get();

            // 添加 display_name 字段
            foreach ($versions as $version) {
                $projectName = $projectMap[$version->project_id] ?? '';
                $version->display_name = $version->name . ' - ' . $projectName;
            }

            return $versions;
        }

        /**
         * 根据事项ID获取事项类型
         * @param int $issueId
         * @return string
         */
        public function getIssuetrackerName(int $issueId) :string
        {
            return $this->model->join('trackers', 'issues.tracker_id', '=', 'trackers.id')->where('issues.id', $issueId)->value('name') ?? '';
        }

        /**
         * 批量获取事项的自定义字段
         * @param array $issueIds 事项ID数组
         * @return array 按事项ID分组的自定义字段数组
         */
        private function getIssuesCustomFields(array $issueIds): array
        {
            if (empty($issueIds)) {
                return [];
            }

            $customValues = \App\Model\Redmine\CustomValuesModel::query()
                ->whereIn('customized_id', $issueIds)
                ->where('customized_type', 'Issue')
                ->get()
                ->toArray();

            $result = [];
            foreach ($customValues as $value) {
                $issueId = $value['customized_id'];
                if (!isset($result[$issueId])) {
                    $result[$issueId] = [];
                }
                $result[$issueId][] = [
                    'id' => $value['custom_field_id'],
                    'value' => $value['value']
                ];
            }

            return $result;
        }

        /**
         * 批量获取事项的关注人
         * @param array $issueIds 事项ID数组
         * @return array 按事项ID分组的关注人数组
         */
        private function getIssuesWatchers(array $issueIds): array
        {
            if (empty($issueIds)) {
                return [];
            }

            $watchers = \App\Model\Redmine\WatchersModel::query()
                ->whereIn('watchable_id', $issueIds)
                ->where('watchable_type', 'Issue')
                ->get()
                ->toArray();

            $result = [];
            foreach ($watchers as $watcher) {
                $issueId = $watcher['watchable_id'];
                if (!isset($result[$issueId])) {
                    $result[$issueId] = [];
                }
                $result[$issueId][] = $watcher['user_id'];
            }

            return $result;
        }

        /**
         * 批量获取事项的多人指派
         * @param array $issueIds 事项ID数组
         * @return array 按事项ID分组的指派用户数组
         */
        private function getIssuesAssignedUsers(array $issueIds): array
        {
            if (empty($issueIds)) {
                return [];
            }

            $assignedUsers = \App\Model\Redmine\IssueAssignedModel::query()
                ->whereIn('issue_id', $issueIds)
                ->get()
                ->toArray();

            $result = [];
            foreach ($assignedUsers as $assigned) {
                $issueId = $assigned['issue_id'];
                if (!isset($result[$issueId])) {
                    $result[$issueId] = [];
                }
                $result[$issueId][] = $assigned['user_id'];
            }

            return $result;
        }

        /**
         * 获取父事项的日期信息用于子事项同步
         * @param array $parentIssues 父事项数组
         * @param array $childIssues 子事项数组
         * @return array 按父事项ID分组的日期信息
         */
        private function getParentDatesForChildIssues(array $parentIssues, array $childIssues): array
        {
            $result = [];

            // 首先从用户传入的父事项中获取日期信息
            foreach ($parentIssues as $parentIssue) {
                $result[$parentIssue['id']] = [
                    'start_date' => $parentIssue['start_date'] ?? null,
                    'due_date' => $parentIssue['due_date'] ?? null,
                    'fixed_version_id' => $parentIssue['fixed_version_id'] ?? null
                ];
            }

            // 获取子事项中可能存在的其他父事项ID
            $childParentIds = array_unique(array_column($childIssues, 'parent_id'));
            $missingParentIds = array_diff($childParentIds, array_keys($result));

            if (!empty($missingParentIds)) {
                // 从数据库获取缺失的父事项日期信息
                $missingParents = $this->model::query()
                    ->whereIn('id', $missingParentIds)
                    ->select(['id', 'start_date', 'due_date', 'fixed_version_id'])
                    ->get()
                    ->toArray();

                foreach ($missingParents as $parent) {
                    $result[$parent['id']] = [
                        'start_date' => $parent['start_date'],
                        'due_date' => $parent['due_date'],
                        'fixed_version_id' => $parent['fixed_version_id']
                    ];
                }
            }

            return $result;
        }

    }

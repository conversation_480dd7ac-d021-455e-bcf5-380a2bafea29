<?php

namespace App\Core\Services\Project;

use App\Model\Redmine\IssueModel;
use App\Model\TchipBi\UserModel;
use App\Annotation\WorkWxTokenAnnotation;
use Hyperf\Utils\Coroutine;

class CommonService extends \App\Core\Services\BaseService
{
    /**
     * 事项内容通知到企业微信
     * @WorkWxTokenAnnotation(type="agent_tchip_bi")
     * @param $issueId
     * @param $notes
     * @param $contentType
     * @return void
     */
    public function sendWorkWxNotice($issueId, $notes, $contentType = 'issue')
    {
        $issue = IssueModel::query()->where('id', $issueId)->first();
        $names = matchAtUserName($notes);
        if ($issue && count($names) > 0) {
            $redmineIssueService = make(\App\Core\Services\WorkWx\WorkWxMessageService::class);

            $host = env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101');
            switch ($contentType) {
                case 'issue':
                    $content = <<<EOT
Hi %s，
数字天启提醒您：
事项 %s 中@您了，请及时查看
[点击查看详情]({$host}/#/project/detail?issue_id=%d)
EOT;
                    break;
                case 'journal':
                    $content = <<<EOT
Hi %s，
数字天启提醒您：
事项 %s 回复@您了，请及时查看
[点击查看详情]({$host}/#/project/detail?issue_id=%d)
EOT;
                    break;
                default:
                    $content = null;

            }

            if ($content) {
                foreach ($names as $name) {
                    Coroutine::create(function () use ($content, $issueId, $name, $issue, $redmineIssueService) {
                        $user = UserModel::query()->where('name', $name)->where('status', 1)->first();
                        if ($user && $issue) {
                            $content = sprintf($content, $name, $issue->subject, $issueId);
                            $redmineIssueService->sendMarkdown($content, $user->workwx_userid, '', '');
                        }
                    });

                }
            }
        }
    }
}
<?php
declare(strict_types=1);

namespace App\Core\Services\Project;
use App\Core\Services\UserService;
use App\Core\Services\WorkWx\WorkWxMessageService;
use App\Model\Redmine\CategoryModel;
use App\Model\Redmine\ProjectsDescModel;
use Hyperf\Context\Context;
use Hyperf\Di\Annotation\Inject;
use App\Annotation\WorkWxTokenAnnotation;
use function Swoole\Coroutine\Http\get;

class ProjectsDescService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var ProjectsDescModel
     */
    protected $model;

    public function getOverView($id)
    {
        $row = $this->model::query()->with(['descInfo', 'project', 'projectExt'])->find($id);
        if ($row) {
            $row = $row->toArray();
            $row['value_info'] = CategoryModel::query(true)->select(['id', 'name', 'keywords'])
                ->where('type', 'product_desc_status')->where('keywords', $row['value'])->first();
            $row['value_info'] = $row['value_info'] ? $row['value_info']->toArray() : [];
            $row['desc_text'] = $row['desc_info']['name'] ?? '';
            $row['value_text'] = $row['value_info']['name'] ?? '';
            $row['project_text'] = $row['project']['name'] ?? '';
        }
        return $row;
    }

    /**
     * 面向接口的重发上线信息函数(异步队排执行版)
     * @param $id projects_desc.id
     * @param $values [notification_member: [51], notification_type: ["mail", "workwx"], user_id]
     * @return bool
     */
    public function doResendDesc($id, $values)
    {
        $values['user_id'] = getRedmineUserId();
        make(\App\Core\Services\Queue\Redmine\ProductDescPushQueue::class)->push(['desc_id' => $id, 'values' => $values]);
        return true;
    }

    /**
     * 直接调用的重发上线信息函数(直接执行版)
     * @param $id
     * @param $values [notification_member: [51], notification_type: ["mail", "workwx"], user_id] user_id为接口传入，因为队列线程无法获取接口的UID
     * @return void
     */
    public function resendDesc($id, $values)
    {
        $desc = $this->getOverView($id);
        if ($desc) {
            if (!empty($values['notification_member']) && !empty($values['notification_type'])) {
                $users = is_array($values['notification_member']) ? $values['notification_member'] : explode(',', (string) $values['notification_member']);
                $userService = make(UserService::class);
                foreach ($users as $uid) {
                    $user = $userService->userInfoByThirdUserId($uid);
                    if ($user) {
                        // 发送Email
                        if (in_array('mail', $values['notification_type'])) {
                            $this->sendEmail($user, $desc, $user['biz_mail'], $values['user_id'] ?? null);
                        }
                        // 发送企微
                        if (in_array('workwx', $values['notification_type'])) {
                            $this->sendWorkwx($user, $desc, $user['workwx_userid'], $values['user_id'] ?? null);
                        }
                    }
                }
            }
        }
        return true;
    }

    /**
     * 发送Email通知
     * @param $user 收接内容的用户数据 userService->userInfoByThirdUserId()
     * @param $desc projects_desc 内容 this->getOverView()
     * @return void
     */
    public function sendEmail($user, $desc, $mail, $sendUserId = null)
    {
        // 属性内容
        $color = '#333333';
        if (in_array($desc['value'], [4])) {
            $color = 'orange';
        } else if (in_array($desc['value'], [1, 5])) {
            $color = 'green';
        }
        $descUrl        = $desc['url'] ? "<a href='{$desc['url']}' style='color:{$color}'>{$desc['url']}</a>" : '';
        $detailAttr = "属性 {$desc['desc_text']}  变更为 <span style='color:{$color}'>{$desc['value_text']}</span>  <br>";
        $detailAttr .= "地址 变更为 {$descUrl} <br>";

        $noticeUser = make(\App\Core\Services\Redmine\UserService::class)->getUserInfo($sendUserId);
        $updateMessage = "更新时间：{$desc['updated_at']} <br />" . (!empty($noticeUser['name']) ? "通知人员：{$noticeUser['name']}" : '') . "<br>";

        // 显示所在选项卡
        $activeName = 'active_name=base';

        $url   = biFrontendHost() . '/project/productDetailsIndex?product_id=' . $desc['project_id'] . ($activeName ? "&$activeName" : '');
        $html = <<<ht
<p> Hi {$user['name']}， </p>
<p>[ {$desc['project']['name']} ] 产品上线信息通知 : <br />
{$updateMessage}
</p>
<br/>
{$detailAttr}
<br/>
<p>点击查看详情 <a href="{$url}">http://bi.t-firefly.com:2101/</a></p>
<p>以上信息由系统发出，如有疑问请联系管理员。</p>
ht;

        sendEmail('产品上线信息通知', $html, $mail);
    }

    /**
     * 发送workwx通知
     * @WorkWxTokenAnnotation(type="agent_tchip_bi")
     * @param $user 收接内容的用户数据 userService->userInfoByThirdUserId()
     * @param $desc projects_desc 内容 this->getOverView()
     * @return void
     */
    public function sendWorkwx($user, $desc, $wxId, $sendUserId = null)
    {
        $workWxMessageService = make(WorkWxMessageService::class);
        $color = 'comment';
        if (in_array($desc['value'], [4])) {
            $color = 'warning';
        } else if(in_array($desc['value'], [1, 5])) {
            $color = 'info';
        }

        $noticeUser = make(\App\Core\Services\Redmine\UserService::class)->getUserInfo($sendUserId);
        $updateMessage = "更新时间：{$desc['updated_at']} \n" . (!empty($noticeUser['name']) ? "通知人员：{$noticeUser['name']}" : '') . "\n";

        $valueStatus = $desc['value_text'] ?? '未定状态';
        $valueText = !empty($desc['url']) ? "[{$valueStatus}]({$desc['url']})" : $valueStatus;
        $detailText = "属性 {$desc['desc_text']}  变更为 <font color=\"{$color}\"> {$valueText} </font>  \n";
        if (!empty($desc['url'])) {
            $detailText .= "地址 变更为 [{$desc['url']}]({$desc['url']}) \n";
        }

        // 显示所在选项卡
        $activeName = 'active_name=base';

        $url   = biFrontendHost() . '/project/productDetailsIndex?product_id=' . $desc['project_id'] . ($activeName ? "&$activeName" : '');
            $content = <<<EOT
**Hi {$user['name']}，**\n
**{$desc['project']['name']} 产品上线信息通知：**\n
{$updateMessage}
{$detailText}
[点击查看详情]({$url})
EOT;

            $workWxMessageService->sendMarkdown($content, $wxId, '', '');

    }
}
<?php

namespace App\Core\Services\Project;

use App\Model\Redmine\ProjectsInfoModel;
use Hyperf\Di\Annotation\Inject;

class ProjectsInfoService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject
     * @var ProjectsInfoModel
     */
    protected $model;

    /**
     * 根据产品ID更新产品info数据
     * @param int $projectId
     * @param array $values
     * @return int
     */
    public function doEditProjectId(int $projectId, array $values)
    {
        if (isset($values['product_status']) && !isset($values['product_progress'])) {
            $values['product_progress'] = make(\App\Core\Services\Product\ProductService::class)->getProductProgress($values['product_status']);
        }
        return $this->model::query()->where('project_id', $projectId)->update($values);
    }
}
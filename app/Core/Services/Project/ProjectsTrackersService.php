<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 下午2:50
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Project;

use App\Constants\DataBaseCode;
use App\Model\Redmine\ProjectsTrackerModel;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;

/**
 * 项目管理相关接口服务
 */
class ProjectsTrackersService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var ProjectsTrackerModel
     */
    protected $model;

    /**
     * @Inject()
     * @var \App\Core\Services\Redmine\ProjectService
     */
    protected $redmineProjectService;
    


    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        /* @var ProjectsTrackerModel $query */
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        return $query->select('trackers.*')->join('trackers', 'projects_trackers.tracker_id', '=', 'trackers.id')->orderBy($sort, $order)->get();
    }

    public function doEditTrackers($projectId, array $values)
    {
        return Db::connection(DataBaseCode::TCHIP_REDMINE)->transaction(function () use($projectId, $values) {
            if (count($values) > 0) {
                $this->model::query()->where('project_id', $projectId)->whereNotIn('tracker_id', $values)->delete();
                foreach ($values as $value) {
                    if (!$this->model::query()->where('project_id', $projectId)->where('tracker_id', $value)->first()) {
                        $this->model::create(['project_id' => $projectId, 'tracker_id' => $value]);
                    }
                }
            } else {
                $this->model::query()->where('project_id', $projectId)->delete();
            }
            return true;
        });
    }
}
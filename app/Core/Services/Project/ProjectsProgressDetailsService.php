<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2023/12/29 下午5:24
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Project;

use App\Constants\DataBaseCode;
use App\Constants\ProductCode;
use App\Constants\StatusCode;
use App\Core\Services\Project\ProgressService;
use App\Core\Services\Project\CategoryService;
use App\Core\Services\Setting\SettingsService;
use App\Exception\AppException;
use App\Model\Model;
use App\Model\Redmine\CategoryModel;
use App\Model\Redmine\ProductModel;
use App\Model\Redmine\ProductPlatformModel;
use App\Model\Redmine\ProjectsProgressModel;
use App\Model\Redmine\ProjectsProgressDetailsModel;
use App\Model\Redmine\ProjectsWatcherModel;
use App\Model\Redmine\MemberModel;
use App\Model\Redmine\ProjectModel;
use App\Model\Redmine\UserModel;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use App\Core\Services\Queue\Redmine\ProjectsProgressWxQueue;
use App\Core\Services\AuthService;
use App\Core\Services\Redmine\AccountService;

/**
 * 项目跟进信息
 */
class ProjectsProgressDetailsService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var ProjectsProgressModel
     */
    protected $model;

    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        return $query->orderBy($sort, $order)->paginate($limit);
    }

}
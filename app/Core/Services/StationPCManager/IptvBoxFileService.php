<?php

namespace App\Core\Services\StationPCManager;


use Hyperf\Di\Annotation\Inject;
use App\Core\Services\BusinessService;
use App\Core\Utils\Log;
use App\Core\Services\WorkWx\WorkWxMessageService;
use App\Annotation\WorkWxTokenAnnotation;
use App\Mail\StationPCManager\IptvBoxFileChangeMail;
use HyperfExt\Mail\Mail;

class IptvBoxFileService extends BusinessService
{
    /**
     * @Inject()
     * @var \App\Model\StationPCManager\IptvBoxFileModel
     */
    protected $model;

    /**
     * @Inject()
     * @var WorkWxMessageService
     */
    protected $workWxMessageService;

    /**
     * 检测文件是否变化
     * @WorkWxTokenAnnotation(type="agent_tchip_bi")
     * @return void
     */
    public function checkFileChange()
    {
        $files = $this->model->where('status', 1)->where('is_update', 0)->where('is_monitor', '1')->get();
        $files = $files ? $files->toArray() : [];
        if ($files) {
            foreach ($files as $file) {
                $liveSha1 = file_get_contents($file['url']);
                $liveSha1 = md5($liveSha1);
                if ($file['now_sha1'] != $liveSha1) {
                    // 更新为有更新状态
                    $this->model::query()->where('id', $file['id'])->update(['is_update' => 1]);
                    // XiaoJiaJie
                    $content = <<<EOT
数字天启提醒您：
StationPc Manager后台管理系统,
IPTV源文件<font color="warning">{$file['name']}</font>网络文件发生了变化,
请及时查看。
EOT;
                    try {
                        $toMail = env('IPTV_NOTICE_MAIL', '<EMAIL>');
                        $toWorkId = env('IPTV_NOTICE_WORKID', 'XiaoJiaJie');
                        $this->workWxMessageService->sendMarkdown($content, $toWorkId, '', '');
                        $mail = make(IptvBoxFileChangeMail::class, [$file]);
                        Mail::to($toMail)->send($mail);
                        $logMessage = "推送成功";
                    }catch (\Exception $e){
                        $logMessage = "推送失败" . $e->getMessage();
                    }
                    Log::get('system','system')->info('文件:'.$file['name'] . " 发生了变化; {$file['now_sha1']} -> $liveSha1". $logMessage);
                } else {
                    Log::get('system','system')->info('文件:'.$file['name'] .'为最新状态');
                }
            }
        }
        return 1;
    }
}
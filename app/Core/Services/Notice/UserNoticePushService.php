<?php

namespace App\Core\Services\Notice;

use App\Core\Services\Setting\CategoryService;
use App\Core\Services\Notice\NoticeCategoryService;
use App\Model\TchipBi\NoticeCategoryModel;
use App\Model\TchipBi\UserNoticePushModel;
use Hyperf\Di\Annotation\Inject;
use App\Core\Utils\TimeUtils;

class UserNoticePushService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var UserNoticePushModel
     */
    protected $model;

    /**
     * 获取列表
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @param int $limit
     * @return mixed
     */
    public function getUserNoticeList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);

        if ($sort != 'id') {
            $query->orderBy($sort, $order);
        } else {
            $query->orderBy('is_read', 'asc')->orderBy('created_at', 'desc');
        }
        $paginate = $query->paginate($limit);
        $paginate = $paginate ? $paginate->toArray() : [];
        if (!empty($paginate['data'])) {
            $types = make(NoticeCategoryService::class)->typeChildrenList('notice_type');
            $types = $types ? array_column($types, null, 'code') : [];
            foreach ($paginate['data'] as &$datum) {
                $datum['mode_text'] = $types[$datum['notice_type']]['name'] ?? '';
                $datum['created_day'] = TimeUtils::formatCnData($datum['created_at']);
            }
        }
        return $paginate;
    }

    /**
     * 获取网页端用户未读消息数量
     * @param int $userId
     * @return int
     */
    public function getUserUnreadNoticeCount(int $userId)
    {
        $query = $this->model->newQuery();
        $query->where('user_id', $userId)
            ->where('is_read', 0)
            ->where('notice_mode', 'website');
        
        return $query->count();
    }

    /**
     * 获取用户的所有消息类型
     * @param array $filter
     * @param array $op
     * @return bool
     */
    public function getUserNoticeType(int $userId)
    {
        $noticeTypes = $this->model::query()
            ->where('notice_mode', 'website')
            ->where('user_id', $userId)
            ->distinct()
            ->pluck('notice_type')
            ->toArray();


        if (!empty($noticeTypes)) {
            $categories = NoticeCategoryModel::query()
                ->whereIn('code', $noticeTypes)
                ->where('status', 1)
                ->get(['code', 'name'])
                ->toArray();

            $results = array_map(function ($item) {
                return [
                    'type' => $item['code'],
                    'label' => $item['name'],
                ];
            }, $categories);
        } else {
            $results = [];
        }

        return $results;
    }

}
<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/31 上午10:39
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Redmine;

use App\Core\Services\AuthService;
use App\Model\Redmine\ProjectModel;
use App\Model\Redmine\IssueModel;
use App\Model\Redmine\UserModel;
use App\Model\Redmine\TokenModel;
use App\Model\TchipBi\UserThirdModel;
use Hyperf\Di\Annotation\Inject;
use Qbhy\HyperfAuth\AuthManager;

/**
 * 用户操作服务类
 */
class UserService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var UserModel
     */
    protected $model;

    /**
     * @Inject()
     * @var AuthManager
     */
    protected $auth;

    /**
     * @Inject()
     * @var AuthService
     */
    protected $authService;

    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10, $field = ['id', 'firstname', 'lastname'])
    {
        if (!empty($filter['auth'])) {
            if (empty($filter['id'])) {
                $redmineAdmin = UserModel::query()->where('id', getRedmineUserId())->value('admin');
                // 两个系统也不是管理员
                if (!$this->authService->isSuper() && !$redmineAdmin) {
                    $usersIds     = $this->getBiDepartmentRedmineUsers();
                    $filter['id'] = implode(',', $usersIds);
                    $op['id']     = 'IN';
                }
            }
            unset($filter['auth']);
        }

        /* @var UserModel $query */
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        $field    = $field ?: ['id', 'firstname', 'lastname'];
        $paginate = $query->select($field)->paginate($limit);
        return $paginate;
    }

    /**
     * 获取redmine用户token
     * @param $uid
     * @param $action
     * @return \Hyperf\Utils\HigherOrderTapProxy|mixed|\Tightenco\Collect\Support\HigherOrderTapProxy|void
     */
    public function getToken($uid = null, $action = 'api')
    {
        $uid = $uid ?? getRedmineUserId();
        return $uid ? TokenModel::query()->where('user_id', $uid)->where('action', $action)
            ->orderBy('updated_on', 'desc')->value('value') : null;
    }

    /**
     * 用户统计处理事项，项目，关注数量
     * @return array
     */
    public function getCountInfo()
    {
        // 1:新建,2:处理中,3:重开
        $uid                 = getRedmineUserId();
        $issueModel          = make(IssueModel::class);
        $toDoCount           = $issueModel::query()->whereIn('status_id', $issueModel->status_todo)->where('assigned_to_id', $uid)->count();
        $createCount         = $issueModel::query()->where('author_id', $uid)->whereIn('status_id', $issueModel->status_todo)->count();
        $watcherCount        = $issueModel::query()->whereHas('watcher', function ($query) use ($uid) {
            $query->where('user_id', $uid)->where('watchable_type', 'Issue');
        })->whereIn('status_id', $issueModel->status_todo)->count();
        $projectWatcherCount = ProjectModel::query()->whereHas('projectsWatchers', function ($query) use ($uid) {
            $query->where('user_id', $uid);
        })->whereRaw('(select p2.id from projects p2 where p2.parent_id = projects.id limit 1) is null')->count();
        return [
            'todo_count'            => $toDoCount ?? 0,
            'create_count'          => $createCount ?? 0,
            'issue_watcher_count'   => $watcherCount ?? 0,
            'project_watcher_count' => $projectWatcherCount ?? 0,
        ];
    }

    /**
     * 根据用户中bi的部门获取redmine成员
     * @return array|mixed[]
     */
    public function getBiDepartmentRedmineUsers()
    {
        $department     = $this->auth->user()->department ?? [];
        $depUsers       = make(\App\Core\Services\UserService::class)->assignDepartmentUserIds($department);
        $redmineUserIds = UserThirdModel::query()->where('platform', 'redmine')->whereIn('user_id', $depUsers)->pluck('third_user_id');
        return $redmineUserIds ? $redmineUserIds->toArray() : [];
    }

    /**
     * 获取用户信息
     * @param $id
     * @return array|mixed[]|null
     */
    public function getUserInfo($id = null)
    {
        if (!$id) {
            try {
                $id = getRedmineUserId();
            } catch (\Qbhy\HyperfAuth\Exception\GuardException $e) {

            } catch (\Qbhy\HyperfAuth\Exception\UserProviderException $e) {

            }
        }
        if ($id) {
            $user = $this->model::query()->find($id);
        }
        return $user ? $user->toArray() : null;
    }

    //根据id获取用户名字集合
    public function getUserName($userIdArr){
        $userData = $this->model::query()
            ->whereIn('id', $userIdArr)
            ->get()
            ->map(function ($user) {
                return [
                    'id'   => $user->id,
                    'name' => $user->lastname . $user->firstname,
                ];
            })
            ->pluck('name', 'id')
            ->toArray();
        return $userData;
    }

}
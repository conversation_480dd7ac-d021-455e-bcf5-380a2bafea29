<?php

namespace App\Core\Services\KuaidiHundred;

use App\Constants\CommonCode;
use App\Constants\StatusCode;
use App\Exception\AppException;
use GuzzleHttp\Exception\GuzzleException;
use Hyperf\Guzzle\ClientFactory;
use Hyperf\Di\Annotation\Inject;

class KdhBaseService
{
    private $ApiKey;
    private $ReqURL = 'http://api.kuaidi100.com/';

    /**
     * @var \Hyperf\Guzzle\ClientFactory
     */
    private $clientFactory;

    public function __construct(ClientFactory $clientFactory)
    {
        $this->ApiKey = env('KDH_APIKEY');
        $options = [
            'base_uri' => $this->ReqURL
        ];
        $this->clientFactory = $clientFactory->create($options);
    }

    protected function sendPost($uri, $options)
    {
        if (!empty($options['param'])) {
            $options['param'] = json_encode($options['param'], JSON_UNESCAPED_UNICODE);
        }

        if (!empty($options['customer'])) {
            $sign = md5($options['param'].$this->ApiKey.$options['customer']);
            $options['sign'] = strtoupper($sign);
        }

        if (empty($options['customer'])) {
            $options['key'] = $this->ApiKey;
        }
        $options = ['form_params' => $options];

        try {
            $response = $this->clientFactory->request('POST', $uri, $options);
            $responseArr = json_decode($response->getBody()->getContents(), true);
            return $responseArr;
        } catch (GuzzleException $e) {
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }

}
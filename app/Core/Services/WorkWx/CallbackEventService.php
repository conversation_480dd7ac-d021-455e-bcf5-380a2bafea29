<?php

namespace App\Core\Services\WorkWx;

use App\Constants\StatusCode;
use App\Constants\ProductBorrowCode;
use App\Constants\WorkwxApprovalCode;
use App\Core\Services\TchipOa\OaProductBorrowService;
use App\Core\Utils\Log;
use App\Exception\AppException;
use App\Model\TchipBi\OaProductBorrowModel;
use Hyperf\DbConnection\Db;

class CallbackEventService extends WorkWxBaseService
{

    public function handleEvent($content)
    {
        Log::get()->info('workwx_callback_event.接收到企业微信审批回调', is_array($content) ? $content : [$content]);
        $data = null;
        if (!empty($content['ToUserName']) && $content['ToUserName'] === env('CORPID')) {
            if (isset($content['Encrypt']) && isset($content['AgentID']) && isset($content['msg_signature']) && isset($content['nonce'])) {
                $data = $this->decryptMsg($content['msg_signature'], $content['timestamp'], $content['nonce'], $content);
                if ($data) {
                    Log::get()->info('workwx_callback_event.解密数据', is_array($data) ? $data : json_decode($data, true));
                }
            }
        }
        if (!empty($data['Event'])) {
            // 转首字母小写的驼峰
            $event = $str = \Hyperf\Utils\Str::camel($data['Event']);
            if (method_exists($this, $event)) {
                return $this->$event($data);
            }
        }
        throw new AppException(StatusCode::ERR_SERVER, 'decryptMsg error');
    }

    /**
     * 企业微信审批状态修改回调事件
     * SpStatus 申请单状态：1-审批中；2-已通过；3-已驳回；4-已撤销；6-通过后撤销；7-已删除；10-已支付
     * 分支审批人审批状态：1-审批中；2-已同意；3-已驳回；4-已转审
     * StatuChangeEvent 审批申请状态变化类型：1-提单；2-同意；3-驳回；4-转审；5-催办；6-撤销；8-通过后撤销；10-添加备注
     * @param $msg
     * @return void
     */
    public function sysApprovalChange($msg)
    {
        $result = Db::transaction(function () use ($msg) {
            if (!empty($msg['ApprovalInfo']['TemplateId']) && !empty($msg['ApprovalInfo']['SpNo'])) {
                // 第一步先处理保存原来的数据
                $approvalService = make(WorkWxApprovalService::class);
                $row = $approvalService->getOverViewByTemplateNo($msg['ApprovalInfo']['TemplateId'], $msg['ApprovalInfo']['SpNo']);
                $approvalService->doEditWxEvent($row->id ?? 0, $msg['ApprovalInfo']);

                // 暂时只处理物品领用模板
                $lendTemplate = env('APPROVAL_LEND_TEMPLATEID', 'Bs5M5h5YcCEQisyyLTTuBf7mnqtV4QGkc58PZgfdV');
                switch ($msg['ApprovalInfo']['TemplateId']) {
                    case $lendTemplate :
                        $borrow = OaProductBorrowModel::query()->where('template_id', $msg['ApprovalInfo']['TemplateId'])
                            ->where('sp_no', $msg['ApprovalInfo']['SpNo'])->first();
                        $borrowService = make(OaProductBorrowService::class);
                        // 更新
                        $save = [];
                        if ($borrow) {
                            $oldBorrow = $borrow->toArray();
                            // $borrow->sp_status = $msg['ApprovalInfo']['SpStatus'];
                            // $borrow->approver_attr = $msg['ApprovalInfo']['SpRecord']['ApproverAttr'];
                            // $borrow->approver_details = $msg['ApprovalInfo']['SpRecord']['Details'];
                            $save['sp_status'] = $msg['ApprovalInfo']['SpStatus'];
                            $save['approver_attr'] = $msg['ApprovalInfo']['SpRecord']['ApproverAttr'];
                            $save['approver_details'] = $msg['ApprovalInfo']['SpRecord']['Details'];
                            switch ((int) $msg['ApprovalInfo']['SpStatus']) {
                                // 审批-已通过
                                case WorkwxApprovalCode::SP_STATUS_PASSED :
                                    // 借用-借用中
                                    // $borrow->borrow_status = ProductBorrowCode::BORROWING_STATUS;
                                    $save['borrow_status'] = ProductBorrowCode::BORROWING_STATUS;
                                    break;
                                case WorkwxApprovalCode::SP_STATUS_REJRCTED :
                                case WorkwxApprovalCode::SP_STATUS_CANCEL :
                                case WorkwxApprovalCode::SP_STATUS_DELETE :
                                    // case WorkwxApprovalCode::SP_STATUS_PASSEDCANCEL :
                                    // $borrow->borrow_status = ProductBorrowCode::CLOSE_STATUS;
                                    $save['borrow_status'] = ProductBorrowCode::CLOSE_STATUS;
                            }
                            if (isset($save['borrow_status'])) {
                                $save['children'] = [
                                    'b_id' => $borrow->id,
                                    'borrow_status' => $save['borrow_status']
                                ];
                            }
                            $borrowService->saveEdit($borrow->id, $save, 0);
                        } else {
                            // 从企业微信新增
                            $borrow = $borrowService->addToWorkWxEvent($msg['ApprovalInfo']);
                        }
                        return $borrow;

                }

            } else {
                Log::get()->info('sysApprovalChange', is_array($msg) ? $msg : [$msg]);
                throw new AppException(StatusCode::ERR_SERVER, 'do sysApprovalChange fail');
            }
        });
        return $result;
    }
}
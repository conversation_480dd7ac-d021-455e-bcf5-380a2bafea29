<?php
/*
 * @Description: 理念文章服务
 * @Version: 1.0
 * @Autor: 张权江
 * @Date: 2025-03-21 13:47:20
 * @LastEditors: 张权江
 * @LastEditTime: 2025-03-25 17:05:36
 */

namespace App\Core\Services\TchipCulture;

use App\Core\Services\BusinessService;
use App\Model\TchipBi\PhilosophyModel;
use App\Model\TchipBi\PhilosophyRecordModel;
use Hyperf\Di\Annotation\Inject;
use Hyperf\DbConnection\Db;
use Qbhy\HyperfAuth\AuthManager;

class PhilosophyService extends BusinessService
{
    /**
     * @Inject
     * @var PhilosophyModel
     */
    protected $model;

    /**
     * @Inject
     * @var PhilosophyRecordModel
     */
    protected $recordModel;

    /**
     * @Inject
     * @var AuthManager
     */
    protected $auth;

    /**
     * 获取理念列表
     */
    public function getList(array $filter = [], array $op = [], string $sort = 'sort_order', string $order = 'DESC', int $limit = 10)
    {
        // 构建查询参数
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        
        // 添加用户关联
        $query->with('user');
        
        // 获取分页数据
        $paginate = $query->orderBy($sort, $order)->paginate($limit);
        
        return $paginate ? $paginate->toArray() : [];
    }

    /**
     * 获取详情
     */
    public function getDetail($id)
    {
        return $this->model->with(['lastRecords.user', 'user'])->find($id);
    }

    /**
     * 保存理念
     * @param int $id
     * @param array $data
     * @return mixed
     */
    public function save($id, array $data)
    {
        Db::beginTransaction();
        try {

            $data['publisher'] = $this->auth->user()->getId();

            $result = parent::doEdit($id, $data);
            
            // 记录变更历史
            $this->recordModel->create([
                'philosophy_id' => $id ?: $result,
                'content' => $data['content'] ?? '',
                'publisher' => $this->auth->user()->getId(),
                'change_type' => $id ? 'update' : 'create',
                'change_note' => '系统自动记录'
            ]);
            
            Db::commit();
            return $result;
            
        } catch (\Throwable $e) {
            Db::rollBack();
            throw $e;
        }
    }
    
    /**
     * 获取修改历史
     */
    public function getHistory($philosophyId, $limit = 10)
    {
        return $this->recordModel
            ->with('operator')
            ->where('philosophy_id', $philosophyId)
            ->orderBy('created_at', 'desc')
            ->paginate($limit)
            ->toArray();
    }
}
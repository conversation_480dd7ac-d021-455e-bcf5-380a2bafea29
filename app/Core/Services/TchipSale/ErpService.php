<?php

namespace App\Core\Services\TchipSale;

use App\Constants\CacheCode;
use App\Constants\StatusCode;
use App\Constants\TchipErpCode;
use App\Core\Utils\Log;
use App\Exception\AppException;
use App\Model\TchipBi\ErpWarehouseModel;
use App\Model\TchipBi\UserModel;
use App\Model\TchipSale\LinkageModel;
use App\Model\TchipSale\ErpStockDayModel;
use Hyperf\Di\Annotation\Inject;
use App\Annotation\WorkWxTokenAnnotation;
use App\Core\Services\WorkWx\WorkWxMessageService;
use App\Annotation\CrotabLogsAnnotation;

class ErpService extends SaleBaseService
{
    /**
     * @Inject()
     * @var \Hyperf\Contract\StdoutLoggerInterface
     */
    private $logger;

    /**
     * @Inject()
     * @var WorkWxMessageService
     */
    protected $workWxMessageService;

    /* 海外货位代码 */
    const LOC_OVERSEAS = '018';

    /**
     * @Inject
     * @var LinkageService
     */
    public $linkageService;

    public function syncErpProductStatus()
    {
        Log::get('system', 'system')->info('==============================================================================');
        Log::get('system', 'system')->info('开始执行同步产品ERP状态任务');
        $linkage    = LinkageModel::query()->where('material', '<>', '')->where('delete_time', 0)->groupBy('material')->get();
        $linkage    = $linkage ? $linkage->toArray() : [];
        $linkageArr = [];
        $materials  = [];
        $i          = 0;
        $j          = 1;
        foreach ($linkage as $value) {
            $material = trim($value['material']);
            $linkageArr[$material] = $value;
            $materials[$i][] = $material;
            if(count($materials[$i]) >= 100) {
                $i++;
            }
        }
        if ($materials) {
            foreach ($materials as $materialItem) {
                $materialParam = implode(',', $materialItem);
                // $this->logger->info('firefly_erpapi/public/index.php/stock/index/getgoodsbatch?codes='.$materialParam);
                $erpData       = $this->sendRequest('firefly_erpapi/public/index.php/stock/index/getgoodsbatch?codes='.$materialParam, []);
                if (isset($erpData['code']) && $erpData['code'] == 1 && !empty($erpData['data'])) {
                    foreach ($erpData['data'] as $datum) {
                        $material = trim($datum['CODE']);
                        // Log::get('system', 'system')->info("[同步产品ERP状态操作] 产品:{$datum['CODE']}，当时状态{$linkageArr[$material]['status']} 数量{$j}/".count($linkage));
                        if (!empty($linkageArr[$material])) {
                            $newStatus = $datum['DUSE'] == 1 ? $datum['DUSE'] : 0;
                            if($newStatus != $linkageArr[$material]['status']){
                                LinkageModel::query()->where(['id' => $linkageArr[$material]['id']])->update(['status' => $newStatus]);
                                $statusText = $newStatus == 1 ? '正常' : '关闭';
                                Log::get('system', 'system')->info("[同步产品ERP状态操作] 产品:{$datum['CODE']},状态已更新为:{$statusText}");
                            }
                            // $result = LinkageModel::query()->where(['id' => $linkageArr[$material]['id']])->update(['status' => $newStatus]);
                            // if ($result) {
                            //     $statusText = $newStatus == 1 ? '正常' : '关闭';
                            //     $this->logger->info("[同步产品ERP状态操作] 产品:{$datum['CODE']},状态已更新为:{$statusText}");
                            //     // $this->line("[同步产品ERP状态操作] 产品:{$datum['CODE']},已停用", 'info');
                            // }
                        }
                        $j++;
                    }
                }
            }
        }
        Log::get('system', 'system')->info('同步产品ERP状态任务执行完成');
        Log::get('system', 'system')->info('==============================================================================');
        return true;
    }

    /**
     * 写入今天统计的erp数据
     * @return int
     */
    public function statisticsByDay(array $materials = [])
    {
        $date       = date('Y-m-d');
        $linkageModel = make(LinkageModel::class);
        $erpStockDayModel = make(ErpStockDayModel::class);
        $model = LinkageModel::query()->where('status', 1)->where('type', 1)->where('material', '<>', '');

        if($materials){
            $model->whereIn('material', $materials);
        }else{
            // 查询今天已统计的产品加入条件中
            $statisticsed = $erpStockDayModel::query()->where(['date_time' => $date])->pluck('material')->toArray();
            if($statisticsed){
                $model->whereNotIn('material', $materials);
            }
        }

        $pros        = $model->groupBy('material')->get();
        $codeGroup   = [];
        $proMaterial = [];
        $limit       = 100;
        $i           = 0;
        foreach ($pros as $pval){
            $material = trim($pval['material']);
            $proMaterial[$material] = $pval;
            $codeGroup[$i][] = $material;
            if(count($codeGroup[$i]) >= $limit) {
                $i++;
            }
        }

        foreach ($codeGroup as $codes) {
            $erpPro = $this->getGoodsByCodes($codes);
            foreach ($erpPro as $eval){
                $code = trim($eval['CODE']);
                if(isset($proMaterial[$code])){
                    $erpMap = [
                        'date_time' => $date,
                        'material' => $code,
                    ];
                    if($erpStockDayModel::query()->where($erpMap)->where('delete_time', 0)->first()){
                        unset($proMaterial[$code]);
                        continue;
                    }
                    // 默认货位
                    $warehouse = $this->warehouseToErp($proMaterial[$code]['warehouse'] ?? 0);
                    $warehouse = $warehouse ? $warehouse : $eval['LOCCODE'];

                    $overseas  = null;
                    $stock     = 0;
                    foreach ($eval['stock_macc'] as $sval){
                        // 默认货位
                        if($sval['LOC'] == $warehouse){
                            $stock = (int) $sval['FOPQTY'];
                        }
                        // 海外货位
                        if($sval['LOC'] == self::LOC_OVERSEAS){
                            $overseas = (int) $sval['FOPQTY'];
                        }
                    }
                    $prod = $this->linkageService->getProductByMaterial($code);
                    // 备货数
                    // $plan      = $stockOrderModel->getProdNumByPlanStatus($proIds['prod_id'], $proIds['size_id'], $proIds['platform_id']);
                    // 占货数
                    // $occuppy = D('Occupy_orderlist')->prodOccupySum($proIds['prod_id'], $proIds['size_id']);
                    // 提货数
                    // $num_today = getDeliveryNum_today($proIds['platform_id'], $proIds['prod_id'], $proIds['size_id']);
                    // $available = $stock;
                    // $realStock = $stock - $plan;
                    $time = time();
                    if (!empty($prod['platform_id']) && !empty($prod['prod_id'])) {
                        $saveData = [
                            'platform_id'     => $prod['platform_id'],
                            'platform'        => $prod['platform_name'],
                            'prod_id'         => $prod['prod_id'],
                            'prod'            => $prod['prod_name'],
                            'size_id'         => $prod['size_id'],
                            'size'            => $eval['NAME'],
                            'material'        => $code,
                            'real_stock'      => $stock,
                            'undelivered'     => $eval['owe_qty'],
                            'duse'            => $eval['DUSE'],
                            'isdefault_cargo' => 1,
                            'cargo_space'     => $warehouse,
                            'create_time'     => $time,
                            'update_time'     => $time,
                            'date_time'       => $date
                        ];

                        $res = $erpStockDayModel::query()->create($saveData);
                        if($overseas !== null && $overseas >= 0){
                            $saveData['isdefault_cargo'] = 0;
                            $saveData['cargo_space']     = self::LOC_OVERSEAS;
                            $saveData['real_stock']      = $overseas;
                            $res = $erpStockDayModel::query()->create($saveData);
                        }
                        unset($proMaterial[$code]);
                        $this->logger->info("料号:{$code},OK");
                    } else {
                        $this->logger->info("料号:{$code},查询linkage表时数据异常");
                    }
                }
            }
        }
        return true;
    }

    /**
     * 检测所有产品在ERP系统中的库存情况
     * @WorkWxTokenAnnotation(type="agent_tchip_bi")
     * @CrotabLogsAnnotation(type="ErpStockCheck")
     * @return void
     */
    public function productStockCheck($originMaterial = null)
    {
        Log::get('system', 'system')->info('==============================================================================');
        Log::get('system', 'system')->info('开始检测销售系统产品ERP库存变化');
        $products = LinkageModel::query()->where(function ($query)use ($originMaterial){
            if ($originMaterial) {
                $query->whereIn('material', $originMaterial);
            }
        })->where('status', 1)->where('type', 1)
            ->where('material', '<>', '')->where('delete_time', 0)->get();
        $products = $products ? $products->toArray() : [];

        // 缓存时间
        $cacheTime = 7200;

        if ($products) {
            $productCount = count($products);
            $materials    = array_column($products, 'material');
            $erpProducts  = $this->getGoodsByCodes($materials);
            $erpCount     = count($erpProducts);
            $erpProducts  = array_column($erpProducts, null, 'CODE');
            // 获取需要通知的人员
            $users = make(UserModel::class)::query()->where('status', 1)->whereJsonContains('department', 2)->get();
            $users = $users ? $users->toArray() : [];
            // 取需要检测的仓位
            $needWarehouse = $this->getNeedCheckStockWarehouse();

            foreach ($products as $key => $product) {

                if (!empty($erpProducts[$product['material']])) {
                    $warehouse = $this->warehouseToErp($product['warehouse'] ?? 0);
                    $warehouse = $warehouse ? : $erpProducts[$product['material']]['LOCCODE'];

                    // 加入产品自身仓位和ERP默认仓位
                    $needWarehouse = array_unique(array_merge($needWarehouse, [$warehouse, $erpProducts[$product['material']]['LOCCODE']]));
                    //

                    $stockCount = 0;
                    foreach ($erpProducts[$product['material']]['stock_macc'] as $erpProduct) {
                        // 匹配需要检测的仓位
                        if (in_array($erpProduct['LOC'], $needWarehouse)) {
                            $cacheKey   = 'sale_product_erp_stock:' . $product['material'] . $erpProduct['LOC'];
                            $cacheStock = getCache($cacheKey);
                            $fopqty     = (int)$erpProduct['FOPQTY'];
                            $stockCount += $fopqty;
                            // 不存在缓存记录并跳过本次
                            if ($cacheStock === false || $cacheStock === null || $cacheStock === '') {
                                setCache($cacheKey, $fopqty, $cacheTime);
                                continue;
                            } else {
                                setCache($cacheKey, $fopqty, $cacheTime);
                            }

                            Log::get('system','system')->info("{$product['material']} : 缓存的库存:{$cacheStock}  最新库存:{$fopqty}");

                            // 当产品仓位对应当前仓位时需要更新产品状态
                            $productStatueMsg = null;

                            // 库存发生变化时开始通知,从0到有，或者是有到0才需要通知
                            if ($cacheStock != $fopqty) {
                                if (($cacheStock == 0 && $fopqty > 0) || ($cacheStock > 0 && $fopqty == 0)) {
                                    if ($fopqty > $cacheStock) {
                                        $event = '增加到';
                                    } else {
                                        $event = '减少到';
                                    }
                                    $productMessage = "检测到产品{$erpProducts[$product['material']]['NAME']} 料号: {$product['material']}，库存发生了变化。仓位 {$erpProduct['LOC_NAME']} 从数量 {$cacheStock} {$event} {$fopqty}。开始通知需要人员";
                                    $productContent = ['销售系统产品数量'=>$productCount, 'ERP系统产品数量'=>$erpCount];
                                    Log::get('system','system')->info('产品进度:' . ($key + 1) . '/' . count($products) . ' ' . $productMessage, $productContent);
                                    $countUser = count($users);
                                    foreach ($users as $ukey => $user) {
                                        $content = <<<EOT
Hi {$user['name']}，
数字天启提醒您：
ERP产品库存发生了变化。
产品:{$erpProducts[$product['material']]['NAME']}
料号:{$product['material']}
[{$erpProduct['LOC_NAME']}] 从库存 <font color="info">{$cacheStock}</font> {$event}到 从库存 <font color="warning">{$fopqty}</font>，
{$productStatueMsg}，请前往电商平台调整
[点击查看详情](http://sale.t-firefly.com:2102/)
EOT;

                                        $logMessage = "通知 产品 {$erpProducts[$product['material']]['NAME']} 料号 {$product['material']} 到用户 {$user['name']} ";
                                        $logContent = ['通知人员数量' => $countUser, '当前' => "({".($ukey + 1)."}/$countUser)"];
                                        // TODO 尝试延时请求微信API，是否还会出现cURL error 6: Could not resolve host
                                        usleep(600);
                                        try {
                                            $this->workWxMessageService->sendMarkdown($content, $user['workwx_userid'], '', '');
                                            // $this->workWxMessageService->sendMarkdown($content, 'XiaoJiaJie', '', '');
                                            // $this->workWxMessageService->sendMarkdown($content, 'foyLin', '', '');break;
                                            $logMessage .= ' 通知成功';
                                        }catch (\Exception $e){
                                            $logMessage = "通知失败，" . $e->getMessage();
                                        }
                                        Log::get('system','system')->info($logMessage, $logContent);
                                    }
                                }
                            }
                        }
                    }

                    // if ($warehouse == $erpProduct['LOC'] && (($fopqty > 0 && $product['sale_status'] != 1) || ($fopqty <= 0 && $product['sale_status'] == 1))) {
                        // if ($fopqty > 0) {
                        //     $productStatueMsg = '产品已上架';
                        // } else {
                        //     $productStatueMsg = '产品已下架';
                        // }
                    // }
                    // 更新状态
                    if (($stockCount > 0 && $product['sale_status'] != 1) || ($stockCount <= 0 && $product['sale_status'] == 1)) {
                        $row = LinkageModel::query()->find($product['id']);
                        if ($row) {
                            $row->sale_status = $stockCount > 0 ? 1 : 0;
                            $row->save();
                        }
                    }
                }
            }

        }
        Log::get('system', 'system')->info('检测结束');
        Log::get('system', 'system')->info('==============================================================================');
    }

    public function getGoodsByName($name, $filter = [], $op = []) : array
    {
        $erpData = $this->sendRequest(
            'firefly_erpapi/public/index.php/stock/index/getgoodsbyname',
            ['query' => ['name' => $name, 'filter' => $filter, 'op' => $op]]
        );
        return $erpData ?: [];
    }

    public function getGoodsByMaterial($material, $filter = [], $op = []) : array
    {
        $erpData = $this->sendRequest(
            'firefly_erpapi/public/index.php/stock/index/getgoodsbycode',
            ['query' => ['code' => $material, 'filter' => $filter, 'op' => $op]]
        );
        return !empty($erpData['data']) ? $erpData['data'] : [];
    }

    /**
     * 根据料号获取ERP产品数据
     * @param array $codes 料号
     * @return array
     */
    public function getGoodsByCodes(array $codes, $bom = 0) : array
    {
        $count  = count($codes);
        $len    = 100;
        $groups = [];
        $i      = 0;
        if ($count > $len) {
            while (true) {
                if ($i >= $count) break;
                $groups[] = array_slice($codes, $i, $len);
                $i       += $len;
            }
        } else {
            $groups[] = $codes;
        }

        $result = [];
        foreach ($groups as $group) {
            $materials = implode(',', $group);
            $erpData = $this->sendRequest('firefly_erpapi/public/index.php/stock/index/getgoodsbatch?codes='.$materials . "&bom={$bom}", []);
            if (isset($erpData['code']) && $erpData['code'] == 1 && !empty($erpData['data'])) {
                $result = array_merge($result, $erpData['data']);
            }
        }
        return $result;
    }

    /**
     * linkage仓位兑换erp仓位
     * @param $id
     * @return string
     */
    public function warehouseToErp($id)
    {
        $cargo = '';
        switch ($id){
            case 1:
                $cargo = '002';
                break;
            case 2:
                $cargo = '004';
                break;
            case 3:
                $cargo = '001';
                break;
            case 4:
                $cargo = '017';
                break;
        }
        return $cargo;
    }



    /**
     * 获取需要检测库存的仓位
     * @return void
     */
    public function getNeedCheckStockWarehouse()
    {
        $warehouse = ErpWarehouseModel::query()->whereRaw("JSON_EXTRACT(ext_option, '$.sale') = true")->pluck('code');
        return $warehouse ? (is_object($warehouse) ? $warehouse->toArray() : $warehouse) : ['001', '002', '004'];
    }

    /**
     * 获取产品定指仓位库存
     * @param $code
     * @param $loc
     * @return int|mixed
     */
    public function getGoodsStock($code, $loc = null)
    {
        $goods = $this->getGoodsByCodes([$code]);
        $goods = !empty($goods[0]) ? $goods[0] : null;
        $num = 0;
        if ($goods) {
            $loc = $loc ? : $goods['LOCCODE'];

            foreach ($goods['stock_macc'] as $stock) {
                // 同等仓位

                if ($loc == $stock['LOC']) {
                    $num += (int) $stock['FOPQTY'];
                    continue;
                }
                // 配件+仓隆配件
                if ($loc == TchipErpCode::LOC_PJ && $stock['LOC'] == TchipErpCode::LOC_CLPJ) {
                    $num += (int) $stock['FOPQTY'];
                    continue;
                }

                // 成品+研发样品
                if ($loc == TchipErpCode::LOC_CP && $stock['LOC'] == TchipErpCode::LOC_YFYP) {
                    $num += (int) $stock['FOPQTY'];
                    continue;
                }
            }
        }
        return $num;
    }

    /**
     * 获取产品的ERP系统中所有已借用的数据
     * @param array $codes
     * @return array
     */
    public function getGoodsBorrowCount(array $codes)
    {
        $erpGoods = $this->getGoodsByCodes($codes);
        $borrowWare = $this->getErpBorrowWarehouse();
        $borrowWare = $borrowWare ? array_column($borrowWare, 'CODE') : [];
        $result = [];
        foreach ($erpGoods as $goods) {
            $item = [
                'CODE' => $goods['CODE'],
                'borrow_count' => 0,
                'loc' => [],
            ];
            foreach ($goods['stock_macc'] as $stock) {
                if (in_array($stock['LOC'], $borrowWare)) {
                    $item['borrow_count'] += (int) $stock['FOPQTY'];
                    $item['loc'][] = $stock;
                }
            }
            $result[] = $item;
        }
        return $result;
    }

    /**
     * 获取指定产品的ERP系统中所有已借用的数据
     * @param array $codes
     * @return array
     */
    public function getGoodsBorrowDetails($code)
    {
        $erpGoods = $this->getGoodsByCodes([$code]);
        $erpGoods = $erpGoods[0] ?? [];
        $borrowWare = $this->getErpBorrowWarehouse();
        $borrowWare = $borrowWare ? array_column($borrowWare, 'CODE') : [];
        $result = [];
        if ($erpGoods['stock_macc']) {
            foreach ($erpGoods['stock_macc'] as $stock) {
                if (in_array($stock['LOC'], $borrowWare)) {
                    $result[] = [
                        'loc_name' => $stock['LOC_NAME'],
                        'stock' => (int) $stock['FOPQTY'],
                    ];
                }
            }
        }
        return $result;
    }

    /**
     * 获取ERP所有借用仓位
     * @return array
     */
    public function getErpBorrowWarehouse()
    {
        $erpData = $this->sendRequest('firefly_erpapi/public/index.php/stock/loc', []);
        $loc = [];
        if (!empty($erpData['data'])) {
            foreach ($erpData['data'] as $datum) {
                if ($datum['TYPES'] == 1 && $datum['VIRTUAL'] == 0 && strpos($datum['NAME'], '借用仓') !== false) {
                    $loc[] = $datum;
                }
            }
        }
        return $loc;
    }

    /**
     * 获取ERP所有借用仓位
     * @return array
     */
    public function getErpWarehouse() : array
    {
        $erpData = $this->sendRequest('firefly_erpapi/public/index.php/stock/loc', []);
        return $erpData['data'] ?? [];
    }

    /**
     * 根据仓库名称获取仓库数据
     * @param $query
     * @return void
     */
    public function getLocByName($locName)
    {
        return $this->getLocOverView(['filter' => ['NAME' => $locName]]);
    }

    /**
     * 获取仓库数据(单条)
     * @param $query
     * @return array|mixed
     */
    public function getLocOverView($query)
    {
        return $this->sendUri('firefly_erpapi/public/index.php/stock/loc/overView', $query);
    }

    /**
     * 根据仓库编码获取该仓库的所有库存
     * @param $locCode
     * @return array|mixed
     */
    public function getErpBorrowProductByLoc($locCode)
    {
        return $this->getErpBorrowProductList(['filter' => ['LOC' => $locCode, 'FOPQTY' => 0], 'op' => ['FOPQTY' => '>']]);
    }

    /**
     * 获取ERP借用中的产品(多条)
     * @param array $query
     * @return array|mixed
     */
    public function getErpBorrowProductList(array $query)
    {
        return $this->sendUri('firefly_erpapi/public/index.php/stock/macc/getList', $query);
    }

    public function sendUri($uri, $query = null)
    {
        $erpData = $this->sendRequest($uri, ['query' => $query]);
        if (isset($erpData['code'])) {
            return $erpData['data'] ?? [];
        }
        return $erpData;
    }

    /**
     * 获取ERP借用中的产品(多条)
     * @param array $query
     * @return array|mixed
     */
    public function getLocList() : array
    {

        $data = getCache(CacheCode::ERP_LOC_KEY);
        $data = null;
        if (!$data) {
            $data = $this->sendRequest('firefly_erpapi/public/index.php/stock/loc/index', []);
            $data = !empty($data['data']) ? $data['data'] : [];
            if ($data) {
                setCache(CacheCode::ERP_LOC_KEY, $data);
            }
        }
        return $data ?? [];
    }



    // 获取ERP其它仓位
    public function getOtherLoc()
    {
        $locs = getCache(CacheCode::ERP_OTHERLOC_KEY);
        if (!$locs) {
            $locs = $this->getLocList();
            foreach ($locs as $key => $loc) {
                if (strpos($loc['NAME'], '成品仓') !== false || strpos($loc['NAME'], '配件仓') !== false || strpos($loc['NAME'], '物料仓') !== false) {
                    unset($locs[$key]);
                }
            }
            if ($locs) {
                setCache(CacheCode::ERP_OTHERLOC_KEY, $locs);
            }
        }
        return $locs;
    }

    // 获取ERP主要仓位
    public function getMasterLoc()
    {
        $locs = getCache(CacheCode::ERP_MASTERLOC_KEY);
        if (!$locs) {
            $locs = $this->getLocList();
            foreach ($locs as $key => $loc) {
                if (strpos($loc['NAME'], '成品仓') === false && strpos($loc['NAME'], '配件仓') === false && strpos($loc['NAME'], '物料仓') === false) {
                    unset($locs[$key]);
                }
            }
            if ($locs) {
                $locs = array_values($locs);
                setCache(CacheCode::ERP_MASTERLOC_KEY, $locs);
            }
        }
        return $locs;
    }
}
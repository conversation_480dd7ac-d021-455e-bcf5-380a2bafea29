<?php

namespace App\Core\Utils;


use App\Constants\StatusCode;
use App\Exception\AppException;
use App\Model\TchipBi\AttachmentModel;
use Qbhy\HyperfAuth\AuthManager;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpMessage\Upload\UploadedFile;
use Qbhy\HyperfAuth\Exception\AuthException;

/**
 * 文件上传类
 */
class Upload
{

    /**
     * 验证码有效时长
     * @var int
     */
    protected static $expire = 120;

    /**
     * 最大允许检测的次数
     * @var int
     */
    protected static $maxCheckNums = 10;

    protected $merging = false;

    protected $chunkDir = null;

    protected $isTest = false;

    protected $config = [];

    protected $error = '';

    protected $saveName = null;

    /**
     * @Inject()
     * @var AuthManager
     */
    protected $auth;

    /**
     * @var \think\File
     */
    protected $file = null;
    protected $fileInfo = null;

    public function __construct($file = null)
    {
        $this->config = [
            'maxsize' => env('UPLOAD_MAX_SIZE', '100mb'),
            'mimetype'  => env('UPLOAD_MIME_TYPE', 'jpg,png,bmp,jpeg,gif,zip,rar,xls,xlsx,wav,mp4,mp3,pdf,7z,img,txt,webp,ppt,doc,docx'),
            'savekey'   => '/uploads/{year}{mon}{day}/{filemd5}{.suffix}',
        ];
        $this->chunkDir = BASE_PATH . '/runtime/chunks';
        if ($file) {
            $this->setFile($file);
        }
    }

    public function getFile()
    {
        return $this->file;
    }

    public function setFile($file)
    {
        if (empty($file)) {
            throw new AppException(StatusCode::ERR_SERVER, __('common.No_file_upload_or_server_upload_limit_exceeded'));
        }
        $fileInfo = $file->toArray();
        $suffix = strtolower(pathinfo($fileInfo['name'], PATHINFO_EXTENSION));
        $suffix = $suffix && preg_match("/^[a-zA-Z0-9]+$/", $suffix) ? $suffix : 'file';
        $fileInfo['suffix'] = $suffix;
        $fileInfo['imagewidth'] = 0;
        $fileInfo['imageheight'] = 0;
        $fileInfo['tmp_name'] =  $fileInfo['tmp_name'] ?? $fileInfo['tmp_file'];

        $this->file = $file;
        $this->fileInfo = $fileInfo;
        $this->checkExecutable();
    }

    protected function checkExecutable()
    {
        //禁止上传PHP和HTML文件
        if (in_array($this->fileInfo['type'], ['text/x-php', 'text/html']) || in_array($this->fileInfo['suffix'], ['php', 'html', 'htm'])) {
            throw new AppException(StatusCode::ERR_SERVER, __('common.Uploaded_file_format_is_limited'));
        }
        return true;
    }

    protected function checkMimetype()
    {
        $mimetypeArr = explode(',', strtolower($this->config['mimetype']));
        $typeArr = explode('/', $this->fileInfo['type']);
        //验证文件后缀
        if ($this->config['mimetype'] === '*'
            || in_array($this->fileInfo['suffix'], $mimetypeArr) || in_array('.' . $this->fileInfo['suffix'], $mimetypeArr)
            || in_array($this->fileInfo['type'], $mimetypeArr) || in_array($typeArr[0] . "/*", $mimetypeArr)) {
            return true;
        }
        throw new AppException(StatusCode::ERR_SERVER, __('common.Uploaded_file_format_is_limited'));
    }

    protected function checkImage($force = false)
    {
        //验证是否为图片文件
        if (in_array($this->fileInfo['type'], ['image/gif', 'image/jpg', 'image/jpeg', 'image/bmp', 'image/png', 'image/webp']) || in_array($this->fileInfo['suffix'], ['gif', 'jpg', 'jpeg', 'bmp', 'png', 'webp'])) {
            $imgInfo = getimagesize($this->fileInfo['tmp_name']);
            if (!$imgInfo || !isset($imgInfo[0]) || !isset($imgInfo[1])) {
                throw new AppException(StatusCode::ERR_SERVER, __('common.Uploaded_file_is_not_a_valid_image'));
            }
            $this->fileInfo['imagewidth'] = isset($imgInfo[0]) ? $imgInfo[0] : 0;
            $this->fileInfo['imageheight'] = isset($imgInfo[1]) ? $imgInfo[1] : 0;
            return true;
        } else {
            return !$force;
        }
    }

    protected function checkSize()
    {
        preg_match('/([0-9\.]+)(\w+)/', $this->config['maxsize'], $matches);
        $size = $matches ? $matches[1] : $this->config['maxsize'];
        $type = $matches ? strtolower($matches[2]) : 'b';
        $typeDict = ['b' => 0, 'k' => 1, 'kb' => 1, 'm' => 2, 'mb' => 2, 'gb' => 3, 'g' => 3];
        $size = (int)($size * pow(1024, isset($typeDict[$type]) ? $typeDict[$type] : 0));
        if ($this->fileInfo['size'] > $size) {
            throw new AppException(StatusCode::ERR_SERVER, __('common.File_is_too_big',
                ['size1' => round($this->fileInfo['size'] / pow(1024, 2), 2),
                'size2' => round($size / pow(1024, 2), 2)]));
        }
    }

    public function getSuffix()
    {
        return $this->fileInfo['suffix'] ?: 'file';
    }

    public function getSavekey($savekey = null, $filename = null, $md5 = null)
    {
        if ($filename) {
            $suffix = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            $suffix = $suffix && preg_match("/^[a-zA-Z0-9]+$/", $suffix) ? $suffix : 'file';
        } else {
            $suffix = $this->fileInfo['suffix'];
        }
        $filename = $filename ? : ($suffix ? substr($this->fileInfo['name'], 0, strripos($this->fileInfo['name'], '.')) : $this->fileInfo['name']);
        $md5 = $md5 ? : md5_file($this->fileInfo['tmp_name']);
        $replaceArr = [
            '{year}'     => date("Y"),
            '{mon}'      => date("m"),
            '{day}'      => date("d"),
            '{hour}'     => date("H"),
            '{min}'      => date("i"),
            '{sec}'      => date("s"),
            '{random}'   => Random::alnum(16),
            '{random32}' => Random::alnum(32),
            '{filename}' => substr($filename, 0, 100),
            '{suffix}'   => $suffix,
            '{.suffix}'  => $suffix ? '.' . $suffix : '',
            '{filemd5}'  => $md5,
        ];
        $savekey = $savekey ? : $this->config['savekey'];
        $savekey = str_replace(array_keys($replaceArr), array_values($replaceArr), $savekey);

        return $savekey;
    }

    /**
     * 清理分片文件
     * @param $chunkid
     */
    public function clean($chunkid)
    {
        if (!preg_match('/^[a-z0-9\-]{36}$/', $chunkid)) {
            throw new AppException(StatusCode::ERR_SERVER, __('Invalid parameters'));
        }
        $iterator = new \GlobIterator($this->chunkDir . DS . $chunkid . '-*', FilesystemIterator::KEY_AS_FILENAME);
        $array = iterator_to_array($iterator);
        foreach ($array as $index => &$item) {
            $sourceFile = $item->getRealPath() ?: $item->getPathname();
            $item = null;
            @unlink($sourceFile);
        }
    }

    /**
     * 合并分片文件
     * @param string $chunkid
     * @param int    $chunkcount
     * @param string $filename
     * @return attachment|\think\Model
     * @throws AppException()StatusCode::self::ERR_SERVER,
     */
    public function merge($chunkid, $chunkcount, $filename)
    {
        if (!preg_match('/^[a-z0-9\-]{36}$/', $chunkid)) {
            throw new AppException(StatusCode::ERR_SERVER, __('Invalid parameters'));
        }

        $filePath = $this->chunkDir . DS . $chunkid;

        $completed = true;
        //检查所有分片是否都存在
        for ($i = 0; $i < $chunkcount; $i++) {
            if (!file_exists("{$filePath}-{$i}.part")) {
                $completed = false;
                break;
            }
        }
        if (!$completed) {
            $this->clean($chunkid);
            throw new AppException(StatusCode::ERR_SERVER, __('Chunk file info error'));
        }

        //如果所有文件分片都上传完毕，开始合并
        $uploadPath = $filePath;

        if (!$destFile = @fopen($uploadPath, "wb")) {
            $this->clean($chunkid);
            throw new AppException(StatusCode::ERR_SERVER, __('Chunk file merge error'));
        }
        if (flock($destFile, LOCK_EX)) { // 进行排他型锁定
            for ($i = 0; $i < $chunkcount; $i++) {
                $partFile = "{$filePath}-{$i}.part";
                if (!$handle = @fopen($partFile, "rb")) {
                    break;
                }
                while ($buff = fread($handle, filesize($partFile))) {
                    fwrite($destFile, $buff);
                }
                @fclose($handle);
                @unlink($partFile); //删除分片
            }

            flock($destFile, LOCK_UN);
        }
        @fclose($destFile);

        $attachment = null;
        try {
            $file = new File($uploadPath);
            $info = [
                'name'     => $filename,
                'type'     => $file->getMime(),
                'tmp_name' => $uploadPath,
                'error'    => 0,
                'size'     => $file->getSize()
            ];
            $file->setSaveName($filename)->setUploadInfo($info);
            $file->isTest(true);

            //重新设置文件
            $this->setFile($file);

            unset($file);
            $this->merging = true;

            //允许大文件
            $this->config['maxsize'] = "1024G";

            $attachment = $this->upload();
        } catch (\Exception $e) {
            @unlink($destFile);
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
        return $attachment;
    }

    /**
     * 分片上传
     * @throws AppException()StatusCode::self::ERR_SERVER,
     */
    public function chunk($chunkid, $chunkindex, $chunkcount, $chunkfilesize = null, $chunkfilename = null, $direct = false)
    {

        if ($this->fileInfo['type'] != 'application/octet-stream') {
            throw new AppException(StatusCode::ERR_SERVER, __('Uploaded file format is limited'));
        }

        if (!preg_match('/^[a-z0-9\-]{36}$/', $chunkid)) {
            throw new AppException(StatusCode::ERR_SERVER, __('Invalid parameters'));
        }

        $destDir = RUNTIME_PATH . 'chunks';
        $fileName = $chunkid . "-" . $chunkindex . '.part';
        $destFile = $destDir . DS . $fileName;
        if (!is_dir($destDir)) {
            @mkdir($destDir, 0755, true);
        }
        if (!move_uploaded_file($this->file->getPathname(), $destFile)) {
            throw new AppException(StatusCode::ERR_SERVER, __('Chunk file write error'));
        }
        $file = new File($destFile);
        $info = [
            'name'     => $fileName,
            'type'     => $file->getMime(),
            'tmp_name' => $destFile,
            'error'    => 0,
            'size'     => $file->getSize()
        ];
        $file->setSaveName($fileName)->setUploadInfo($info);
        $this->setFile($file);
        return $file;
    }

    /**
     * 普通上传
     * @return \app\common\model\attachment|\think\Model
     * @throws AppException
     */
    public function upload($savekey = null)
    {
        if (empty($this->file)) {
            throw new AppException(StatusCode::ERR_SERVER, __('common.No file upload or server upload limit exceeded'));
        }

        $this->checkSize();
        $this->checkExecutable();
        $this->checkMimetype();
        $this->checkImage();

        $savekey = $savekey ? : $this->getSavekey();
        $savekey = '/' . ltrim($savekey, '/');
        $uploadDir = substr($savekey, 0, strripos($savekey, '/') + 1);
        $fileName = substr($savekey, strripos($savekey, '/') + 1);

        $destDir = BASE_PATH . '/public' . str_replace('/', DIRECTORY_SEPARATOR, $uploadDir);

        // 获取文件sha1值
        $sha1 = hash_file('sha1', $this->fileInfo['tmp_name']);

        //如果是合并文件
        if ($this->merging) {
            // if (!$this->file->check()) {
            //     throw new AppException(StatusCode::ERR_SERVER, $this->file->getError());
            // }
            // $destFile = $destDir . $fileName;
            // $sourceFile = $this->file->getRealPath() ?: $this->file->getPathname();
            // $info = $this->file->getInfo();
            // $this->file = null;
            // if (!is_dir($destDir)) {
            //     @mkdir($destDir, 0755, true);
            // }
            // rename($sourceFile, $destFile);
            // $file = new File($destFile);
            // $file->setSaveName($fileName)->setUploadInfo($info);
        } else {
            $this->move($destDir, $fileName);
            // $result = $file->moveTo($destDir.$fileName);
            // $file->getPath();
            // $file->sve();
            // $file = $this->move($destDir, $fileName);
            // if (!$file) {
            //     // 上传失败获取错误信息
            //     throw new AppException(StatusCode::ERR_SERVER, $this->file->getError());
            // }
        }
        try {
            $uid = $this->auth->user()->getId();
        } catch (AuthException $e) {
            $uid = 0;
        }

        $params = array(
            'user_id'     => $uid,
            'filename'    => substr(htmlspecialchars(strip_tags($this->fileInfo['name'])), 0, 100),
            'filesize'    => $this->fileInfo['size'],
            'filetype'    => $this->getFileType(),
            'imagewidth'  => $this->fileInfo['imagewidth'],
            'imageheight' => $this->fileInfo['imageheight'],
            'imagetype'   => $this->fileInfo['suffix'],
            'imageframes' => 0,
            'mimetype'    => $this->fileInfo['type'],
            'url'         => $uploadDir . $this->getSaveName(),
            'storage'     => 'local',
            'sha1'        => $sha1,
            'extparam'    => '',
        );
        $first = [
            'filename' => $params['filename'],
            'filesize' => $params['filesize'],
            'filetype' => $params['filetype'],
            'url' => $params['url'],
            'sha1' => $params['sha1'],
        ];
        $attachment = AttachmentModel::query()->firstOrCreate($first, $params);
        return $attachment;
    }

    protected function getSaveName()
    {
        return $this->saveName;
    }

    /**
     * @param $file
     * @return mixed|string
     * TODO 完善其它可能出现的编辑
     */
    public function getFileType($file = null)
    {
        $file = $file ?: $this->fileInfo;
        if (is_array($file) && !empty($file['type'])) {
            $type = explode('/', $file['type']);
            return $type[0] ?? '';
        }
        return '';
    }

    /**
     * 移动文件
     * @access public
     * @param  string      $path     保存路径
     * @param  string|bool $savename 保存的文件名 默认自动生成
     * @param  boolean     $replace  同名文件是否覆盖
     * @return false|File
     */
    public function move($path, $savename = true, $replace = true)
    {

        // 检测合法性
        if (!is_file($this->fileInfo['tmp_file']) || !is_uploaded_file($this->fileInfo['tmp_file'])) {
            throw new AppException(StatusCode::ERR_SERVER, __('common.Upload_illegal_files'));
        }

        $path = rtrim($path, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR;
        // 文件保存命名规则
        $saveName = $this->buildSaveName($savename);
        $filename = $path . $saveName;

        // 检测目录
        $this->checkPath(dirname($filename));

        // 不覆盖同名文件
        if (!$replace && is_file($filename)) {
            throw new AppException(StatusCode::ERR_SERVER, __('common.Has_the_same_filename:%s', ['$filename']));
            //
            // $this->error = ['has the same filename: {:filename}', ['filename' => $filename]];
            // return false;
        }

        /* 移动文件 */
        $this->file->moveTo($filename);
        return true;
        // if ($this->isTest) {
        //     rename($this->filename, $filename);
        // } elseif (!move_uploaded_file($this->fileInfo['tmp_file'], $filename)) {
        //     $this->error = 'upload write error';
        //     return false;
        // }
        // 返回 File 对象实例
        // $file = new self($filename);
        // $file->setSaveName($saveName)->setUploadInfo($this->info);
        //
        // return $file;
    }

    /**
     * 获取保存文件名
     * @access protected
     * @param  string|bool $savename 保存的文件名 默认自动生成
     * @return string
     */
    protected function buildSaveName($savename)
    {
        // 自动生成文件名
        if (true === $savename) {
            $savename = date('Ymd') . DIRECTORY_SEPARATOR . md5(microtime(true));
        } elseif ('' === $savename || false === $savename) {
            $savename = $this->fileInfo['name'];
        }

        if (!strpos($savename, '.')) {
            $savename .= '.' . pathinfo($this->fileInfo['name'], PATHINFO_EXTENSION);
        }
        $this->saveName = $savename;
        return $savename;
    }

    /**
     * 检查目录是否可写
     * @access protected
     * @param  string $path 目录
     * @return boolean
     */
    protected function checkPath($path)
    {
        if (is_dir($path) || mkdir($path, 0755, true)) {
            return true;
        }
        throw new AppException(StatusCode::ERR_SERVER, __('common.Directory_creation_failed:%s', [$path]));
    }
}

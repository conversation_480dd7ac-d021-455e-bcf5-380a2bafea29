<?php

namespace App\Mail\Redmine;

use App\Constants\ProductCode;
use App\Model\Redmine\CategoryModel;
use App\Model\TchipBi\UserModel;
use App\Model\TchipBi\UserThirdModel;
use HyperfExt\Mail\Mailable;

class ProductProgressMail extends Mailable
{

    public $service;

    public function __construct($service)
    {
        $this->service = $service;
    }

    /**
     * 构造消息。
     *
     * @return $this
     */
    public function build()
    {
        $title = $this->service->product_progress['type_text'];
        $attachments = [];
        $attachments_link = '';
        $updateMessage = '';
        if ($title == '改版记录') {
            $attachments = $this->service->product_progress['attachment_reversion'];
            if ($attachments) {
                $attachments_link .= "<br> <a>附件:</a> <br>";
            }
            foreach ($attachments as $key => $item) {
                $attachments_link .= "<a href=\"{$item['url']}\">{$item['filename']}</a> <br>";
            }

        }

        $versionText = '';
        if (!empty($this->service->product_progress['version'])) {
            $versionText = "<div>前版本号:{$this->service->product_progress['version_pre']} 版本号:{$this->service->product_progress['version']}</div>";
        }
        if (!empty($this->service->product_progress['modify_at'])) {
            $versionText .= "<div>下单时间: {$this->service->product_progress['modify_at']}</div>";
        }
        $versionText = $versionText ? "<div>{$versionText}</div>" : $versionText;

        // 改版描述
        $describeText = '';
        if (!empty($this->service->product_progress['describe'])) {
            $describeText = "<div>改版描述:{$this->service->product_progress['describe']}</div>";
        }

        // 属性内容
        $detailAttr = '';
        if (!empty($this->service->product_progress['details'])) {

            $propKey = array_column($this->service->product_progress['details'], 'prop_key');
            if (array_intersect(ProductCode::DESC_FIELD, $propKey)) {
                $title = '上线信息';
            }
            // $detailAttr .= '<table style="width: 100%;border-top: 1px solid #999;border-left: 1px solid #999;border-spacing: 0;"><tr><td style="border-bottom: 1px solid #999;border-right: 1px solid #999;">修改属性</td><td style="border-bottom: 1px solid #999;border-right: 1px solid #999;">原来的值</td><td style="border-bottom: 1px solid #999;border-right: 1px solid #999;">最新的值</td></tr>';
            foreach ($this->service->product_progress['details'] as $detail) {
                switch ($detail['property']) {
                    case 'json' :
                        if (!empty($detail['diff_value_text'][0]) && is_array($detail['diff_value_text'][0])) {
                            foreach ($detail['diff_value_text'][0] as $dkey => &$dval) {
                                // if ($dval != $detail['diff_value_text'][1][$dkey]) {
                                //     $detailAttr .= "<tr><td style='border-bottom: 1px solid #999;border-right: 1px solid #999;'>{$dval['name']}</td><td style='border-bottom: 1px solid #999;border-right: 1px solid #999;'> <a href='{$dval['url']}' style='color:{$dval['ext']['color']}'>{$dval['text']}</a> </td><td style='border-bottom: 1px solid #999;border-right: 1px solid #999;'> <a href='{$detail['diff_value_text'][1][$dkey]['url']}' style='color:{$detail['diff_value_text'][1][$dkey]['ext']['color']}'>{$detail['diff_value_text'][1][$dkey]['text']}</a> </td></tr>";
                                // }
                                if ($dval['text'] != $detail['diff_value_text'][1][$dkey]['text']) {
                                    // $detailAttr .= "{$dval['name']} 文案 由  <font color=\"info\">{$dval['text']}</font>  变更为 {$detail['diff_value_text'][1][$dkey]['text']}  \n";
                                    $detailAttr .= "{$dval['name']}更新为 {$detail['diff_value_text'][1][$dkey]['text']}  <br>";
                                }
                                if ($detail['diff_value_text'][1][$dkey]['url']) {
                                    // $detailAttr .= "{$dval['name']} 地址 由  <font color=\"info\">{$dval['url']}</font>  变更为 {$detail['diff_value_text'][1][$dkey]['url']}  \n";
                                    $detailAttr .= "访问地址{$detail['diff_value_text'][1][$dkey]['url']}  <br>";
                                }
                            }
                        }
                        break;
                    case 'desc_attr' :
                        $oldValue = json_decode($detail['old_value'], true);
                        $value = json_decode($detail['value'], true);

                        $status = CategoryModel::query()->whereIn('keywords', [$oldValue['value'], $value['value']])->where('type', 'product_desc_status')->get();
                        $status = $status ? array_column($status->toArray(), null, 'keywords') : [];

                        if ($oldValue['value'] != $value['value']) {
                            // 旧属性处理
                            $oldValueStatus = $status[$oldValue['value']]['name'] ?? '未定状态';
                            $oldColor = '#333333';
                            if (in_array($oldValue['value'], [4])) {
                                $oldColor = 'orange';
                            } else if(in_array($oldValue['value'], [1, 5])) {
                                $oldColor = 'green';
                            }
                            $oldValueText = !empty($oldValue['url']) ? "<a href='{$oldValue['url']}' style='color:{$oldColor}'>{$oldValueStatus}</a>" : "<span style='color:{$oldColor}'>$oldValueStatus</span>";

                            // 新属性处理
                            $valueStatus = $status[$value['value']]['name'] ?? '未定状态';
                            $color = '#333333';
                            if (in_array($value['value'], [4])) {
                                $color = 'orange';
                            } else if(in_array($value['value'], [1, 5])) {
                                $color = 'green';
                            }
                            $valueText = !empty($value['url']) ? "<a href='{$value['url']}' style='color:{$color}'>{$valueStatus}</a>" : "<span style='color:{$color}'>$valueStatus</span>";

                            $detailAttr .= "属性 {$detail['prop_key_text']} 由  $oldValueText  变更为 $valueText  <br>";
                        }
                        // 地址改变
                        if ($oldValue['url'] != $value['url']) {
                            $detailAttr .= "{$detail['prop_key_text']}地址 由 {$oldValue['url']} 变更为 <a href='{$value['url']}'>{$value['url']}</a> <br>";
                        }
                        break;
                    default:
                        // $detailAttr .= "<tr><td style='border-bottom: 1px solid #999;border-right: 1px solid #999;'>{$detail['prop_name']}</td><td style='border-bottom: 1px solid #999;border-right: 1px solid #999;'>{$detail['diff_value_text'][0]}</td><td style='border-bottom: 1px solid #999;border-right: 1px solid #999;'>{$detail['diff_value_text'][1]}</td></tr>";
                        $detailAttr .= "属性 {$detail['prop_name']} 由 {$detail['diff_value_text'][0]} 变更为 {$detail['diff_value_text'][1]}  <br>";
                }
            }
            // $detailAttr .= '</table>';
        }

        $description = !empty($this->service->product_progress['description_html']) ? $this->service->product_progress['description_html'] : (!empty($this->service->product_progress['description']) ? $this->service->product_progress['description'] : '');

        // 处理markdown换行符
        if (!hasHtmlTags($description)) {
            $descriptionArr = explode("\n", $description);
            $description = '';
            foreach ($descriptionArr as $ar) {
                $description .= $ar . '<br/>';
            }
            $description = !empty($description) ? "<div>{$description}</div>" : '';
        }
        $followUpAuthor= UserThirdModel::query()
            ->select(['id', 'user_id'])
            ->where('third_user_id', $this->service->product_progress['create_user_id'])
            ->with(['user' => function ($query) {
                $query->select(['id', 'name', 'status', 'department']);
            }])
            ->first()->toArray();
        $followUpAuthor = $followUpAuthor['user']['name'];
        $followUpdateTime = date('Y-m-d H:i', strtotime($this->service->product_progress['updated_at']));
        $updateMessage .= "   <br>更新时间：{$followUpdateTime}<br>更新人员：{$followUpAuthor}";

        $titleSubject = $this->service->product_progress['product']['name'] . ' 产品跟进通知';

        // 显示所在选项卡
        $activeName = !empty($this->service->product_progress['version']) ? 'active_name=versionHistory' : 'active_name=workProgress';

        $url = biFrontendHost().'/project/productDetailsIndex?product_id='.$this->service->product_id . ($activeName ? "&$activeName" : '');
        $html1 = <<<ht
    <p> Hi {$this->service->name}， </p>
    <p>{$this->service->product_progress['product']['name']} 有新的{$title} :
    {$updateMessage}
    </p>
    <br/>
    {$versionText}{$describeText}
    {$description}
    {$detailAttr}
    {$attachments_link}
    <br/>
    <p>点击查看详情 <a href="{$url}">http://bi.t-firefly.com:2101/</a></p>
    <p>以上信息由系统发出，如有疑问请联系管理员。</p>
ht;
        return $this
            ->subject($titleSubject)
            ->htmlBody($html1);

    }
}
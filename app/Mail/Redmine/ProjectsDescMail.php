<?php

namespace App\Mail\Redmine;

use App\Constants\ProductCode;
use App\Model\Redmine\CategoryModel;
use App\Model\TchipBi\UserModel;
use App\Model\TchipBi\UserThirdModel;
use HyperfExt\Mail\Mailable;

class ProjectsDescMail extends Mailable
{

    public $service;

    public function __construct($service)
    {
        $this->service = $service;
    }

    /**
     * 构造消息。
     *
     * @return $this
     */
    public function build()
    {
        var_dump($this->service);
        $attachments = [];
        $attachmentsLink = '';
        $updateMessage = '';

        // 属性内容
        $color = '#333333';
        if (in_array($this->service->product_progress['value'], [4])) {
            $color = 'orange';
        } else if(in_array($this->service->product_progress['value']['value'], [1, 5])) {
            $color = 'green';
        }

        $url = $this->service->product_progress['url'] ? "<a href='{$this->service->product_progress['url']}' style='color:{$color}'>{$this->service->product_progress['url']}</a>" : '';
        $detailAttr = "属性 {$this->service->product_progress['desc_text']}  变更为 <span style='color:{$color}'>{$this->service->product_progress['value_text']}</span>  <br>";
        $detailAttr .= "地址 变更为 {$url} <br>";

        $followUpAuthor= $this->service->product_progress['username'] ?? (\App\Model\Redmine\UserModel::query()->find($this->service->product_progress['user_id'])->name ?? '');
        $updateMessage .= "   <br>更新时间：{$this->service->product_progress['updated_at']}<br>更新人员：{$followUpAuthor}";

        $titleSubject = $this->service->product_progress['project']['name'] . ' 产品跟进通知';

        // 显示所在选项卡
        $activeName = 'active_name=base';

        $url = biFrontendHost().'/project/productDetailsIndex?product_id='.$this->service->product_id . ($activeName ? "&$activeName" : '');
        $html1 = <<<ht
<p> Hi {$this->service->name}， </p>
<p>{$this->service->product_progress['project']['name']} 有新的上线信息 :
通知人员：{$updateMessage}
</p>
<br/>
{$detailAttr}
{$attachmentsLink}
<br/>
<p>点击查看详情 <a href="{$url}">http://bi.t-firefly.com:2101/</a></p>
<p>以上信息由系统发出，如有疑问请联系管理员。</p>
ht;

        var_dump($html1);
        return $this
            ->subject($titleSubject)
            ->htmlBody($html1);

    }
}
<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/30 下午8:39
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Listener;

use App\Core\Services\Redmine\AccountService;
use App\Core\Services\UserService;
use App\Core\Utils\Log;
use App\Event\UserLogged;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Psr\Container\ContainerInterface;
use \App\Core\Services\TchipBbs\UserService as BbsUserService;

/**
 * 监听登录后事件
 * @Listener
 */
class UserLoggedListener implements ListenerInterface
{
    /**
     * @var AccountService
     */
    private $redmineAccountService;

    /**
     * @var BbsUserService
     */
    private $bbsUserService;

    public function __construct(ContainerInterface $container)
    {
        $this->redmineAccountService = $container->get(AccountService::class);
        $this->bbsUserService = $container->get(BbsUserService::class);
    }

    /**
     * @inheritDoc
     */
    public function listen(): array
    {
        // TODO: Implement listen() method.
        return [
            UserLogged::class
        ];
    }

    /**
     * @inheritDoc
     */
    public function process(object $event)
    {
        // TODO: Implement process() method.
        // 事件触发后该监听器要执行的代码写在这里，比如该示例下的发送用户注册成功短信等
        // 直接访问 $event 的 user 属性获得事件触发时传递的参数值
        Log::get()->info("=====================================================");
        Log::get()->info("执行登录事件");
        $this->redmineAccountService->initRedmine($event->user->id, $event->user->name, $event->user->biz_mail);
        $this->bbsUserService->initUser($event->user->id, $event->user->name, $event->user->biz_mail);
        Log::get()->info("=====================================================");
    }
}
<?php

declare (strict_types=1);
namespace App\Model\Redmine;

/**
 * @property int $id 
 * @property int $issue_from_id 
 * @property int $issue_to_id 
 * @property string $relation_type 
 * @property int $delay 
 */
class IssueRelationModel extends \App\Model\Redmine\RedmineBaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'issue_relations';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['issue_from_id', 'issue_to_id', 'relation_type', 'delay'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['id' => 'integer', 'issue_from_id' => 'integer', 'issue_to_id' => 'integer', 'delay' => 'integer'];
}
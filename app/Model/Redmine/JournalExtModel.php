<?php

declare (strict_types=1);
namespace App\Model\Redmine;

use Hyperf\Database\Model\SoftDeletes;

/**
 * @property int $id 
 * @property int $journal_id 
 * @property int $parent_id 
 * @property string $quick_code 
 * @property \Carbon\Carbon $created_on 
 * @property \Carbon\Carbon $updated_on 
 * @property string $deleted_on 
 */
class JournalExtModel extends \App\Model\Redmine\RedmineBaseModel
{
    use SoftDeletes;
    const DELETED_AT = 'deleted_on';
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'journal_ext';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['journal_id', 'parent_id', 'quick_code', 'notes_html'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['id' => 'integer', 'journal_id' => 'integer', 'parent_id' => 'integer', 'created_on' => 'datetime', 'updated_on' => 'datetime'];
}
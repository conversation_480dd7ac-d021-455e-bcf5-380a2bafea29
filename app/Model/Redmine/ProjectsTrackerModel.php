<?php

declare (strict_types=1);
namespace App\Model\Redmine;

/**
 */
class ProjectsTrackerModel extends \App\Model\Redmine\RedmineBaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'projects_trackers';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['project_id', 'tracker_id'];

    public $timestamps = false;

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'project_id'              => 'integer',
        'tracker_id'              => 'integer',
    ];
}
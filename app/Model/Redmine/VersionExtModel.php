<?php

declare (strict_types=1);

namespace App\Model\Redmine;

use Hyperf\Cache\Annotation\Cacheable;
use League\HTMLToMarkdown\HtmlConverter;

/**
 * @property int $id
 * @property int $version_id
 * @property int $type
 * @property \Carbon\Carbon $created_on
 * @property \Carbon\Carbon $updated_on
 */
class VersionExtModel extends \App\Model\Redmine\RedmineBaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'version_ext';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['version_id', 'type', 'created_on', 'updated_on', 'responsible_user_id', 'prev_version_id', 'content'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id'                  => 'integer',
        'version_id'          => 'integer',
        'responsible_user_id' => 'integer',
        'type'                => 'string',
        'created_on'          => 'datetime',
        'updated_on'          => 'datetime',
    ];

    protected $appends = ['version_type_text', 'responsible_name', 'record_content_text', 'prev_version', 'responsible_user'];

    public function version()
    {
        return $this->belongsTo('\App\Model\Redmine\VersionModel', 'id', 'version_id');
    }

    public function getVersionTypeTextAttribute()
    {
        $attr = '';
        if (!empty($this->attributes['type'])) {
            $attr = \App\Model\Redmine\CategoryModel::query()->where('type', 'version_record_type')
                ->where('keywords', $this->attributes['type'])->value('name');
        }
        return $attr;
    }

    public function getResponsibleNameAttribute()
    {
        $attr = '';
        if (!empty($this->attributes['responsible_user_id'])) {
//            $row = \App\Model\Redmine\UserModel::query()->where('id', $this->attributes['responsible_user_id'])->first();
            $row = $this->responsibleNameCache($this->attributes['responsible_user_id']);
            if ($row) {
                $attr = $row->lastname . $row->firstname;
            }
        }
        return $attr;
    }
    /**
     * @Cacheable(prefix="responsible_name_", ttl=10)
     */
    public function responsibleNameCache($id)
    {
        return \App\Model\Redmine\UserModel::query()->where('id', $id)->first();
    }

    public function getRecordContentTextAttribute(): string
    {
        return !empty($this->attributes['record_content']) ? strip_tags($this->attributes['record_content']) : '';
    }

    public function getPrevVersionAttribute()
    {
//        return VersionModel::query()->find($this->attributes['prev_version_id']);
        return $this->prevVersionCache($this->attributes['prev_version_id']);
    }
    /**
     * @Cacheable(prefix="prev_version_", ttl=10)
     */
    public function prevVersionCache($id)
    {
        return VersionModel::query()->find($id);
    }

    public function getResponsibleUserAttribute()
    {
//        return UserModel::query()->find($this->attributes['responsible_user_id']);
        return $this->responsibleUserCache($this->attributes['responsible_user_id']);
    }
    /**
     * @Cacheable(prefix="responsible_user_", ttl=10)
     */
    public function responsibleUserCache($id)
    {
        return UserModel::query()->find($id);
    }

    public function getContentAttribute()
    {
        $value = '';
        if (!empty($this->attributes['content'])) {
            if (hasHtmlTags($this->attributes['content'])) {
                $converter = make(HtmlConverter::class);
                $value =  $converter->convert($this->attributes['content']);
            } else {
                $value = $this->attributes['content'];
            }
        }
        return $value;
    }
}
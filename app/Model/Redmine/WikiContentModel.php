<?php

declare (strict_types=1);
namespace App\Model\Redmine;

use Carbon\Carbon;

/**
 * @property int $id 
 * @property int $page_id 
 * @property int $author_id 
 * @property string $text 
 * @property string $comments 
 * @property \Carbon\Carbon $updated_on 
 * @property int $version
 * @property int $usage_type
 * @property string $text_html
 */
class WikiContentModel extends \App\Model\Redmine\RedmineBaseModel
{
    public const UPDATED_AT = 'updated_on';
    public const CREATED_AT = null;
    public const DELETED_AT = null;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'wiki_contents';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['page_id', 'author_id', 'text', 'comments', 'updated_on', 'version', 'text_html', 'usage_type'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['id' => 'integer', 'page_id' => 'integer', 'author_id' => 'integer', 'updated_on' => 'datetime', 'version' => 'integer'];

    public function wikiContentVersions()
    {
        return $this->hasMany('\App\Model\Redmine\WikiContentVersionModel', 'wiki_content_id', 'id');
    }

    public function author()
    {
        return $this->belongsTo('\App\Model\Redmine\UserModel', 'author_id', 'id');
    }

    public function setUpdatedOnAttribute()
    {
        // 将时间往前调8小时使其与redmine数据库原有数据一致
        $this->attributes['updated_on'] =  Carbon::now()->subHours(8);
    }
    public function getUpdatedOnAttribute($value)
    {
        // 取数据时后调8小时使其与redmine数据库原有数据一致
        $carbonInstance = Carbon::parse($value)->addHours(8);
        return $carbonInstance->format('Y-m-d H:i');

    }

    public function page()
    {
        return $this->belongsTo('\App\Model\Redmine\WikiPageModel', 'page_id', 'id');
    }

}
<?php

declare (strict_types=1);

namespace App\Model\Redmine;

use Hyperf\Cache\Annotation\Cacheable;
/**
 * @property int $id
 * @property int $container_id
 * @property string $container_type
 * @property string $filename
 * @property string $disk_filename
 * @property int $filesize
 * @property string $content_type
 * @property string $digest
 * @property int $downloads
 * @property int $author_id
 * @property \Carbon\Carbon $created_on
 * @property string $description
 * @property string $disk_directory
 */
class AttachmentModel extends \App\Model\Redmine\RedmineBaseModel
{
    public $timestamps = false;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'attachments';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['container_id', 'container_type'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['id' => 'integer', 'container_id' => 'integer', 'filesize' => 'integer', 'downloads' => 'integer', 'author_id' => 'integer', 'created_on' => 'datetime'];

    protected $appends = ['name', 'url', 'author_name', 'filesize_text'];

    public function getNameAttribute()
    {
        return $this->attributes['filename'];
    }

    public function getUrlAttribute()
    {
        return env('REDMINE_FILE_URL') . $this->attributes['disk_directory'] . '/' . $this->attributes['disk_filename'];
    }

    public function getAuthorNameAttribute()
    {
        $value = '';
        if (!empty($this->attributes['author_id'])) {
//            $user = UserModel::query()->find($this->attributes['author_id']);
//            if ($user) {
//                $value = $user->lastname . $user->firstname;
//            }
            $allName = $this->getAuthorNameCache('name');
            $name = $allName[$this->attributes['author_id']];
            if ($allName) {
                $value = $name['lastname'] . $name['firstname'];
            }
        }
        return $value;
    }

    /**
     * @Cacheable (prefix="user_author_name_", ttl=10)
     */
    public function getAuthorNameCache($name)
    {
        $usersName= UserModel::query()->select('lastname','firstname', 'id')->get()->toArray();
        return array_column($usersName, null, 'id') ?? [];
    }

    public function getFilesizeTextAttribute()
    {
        $value = '';
        if (!empty($this->attributes['filesize'])) {
            $value = byteSize($this->attributes['filesize']);
        }
        return $value;
    }

    public function getCreatedOnAttribute()
    {
        $value = '';
        if (!empty($this->attributes['created_on'])) {
            $dt = new \DateTime($this->attributes['created_on'],new \DateTimeZone('UTC'));
            $dt->setTimezone(new \DateTimeZone('Asia/Shanghai'));
            $value = $dt->format('Y-m-d H:i');
        }
        return $value;
    }
}
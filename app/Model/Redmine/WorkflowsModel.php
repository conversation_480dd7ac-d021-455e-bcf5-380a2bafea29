<?php

declare (strict_types=1);
namespace App\Model\Redmine;

/**
 * @property int $id 
 * @property int $tracker_id 
 * @property int $old_status_id 
 * @property int $new_status_id 
 * @property int $role_id 
 * @property int $assignee 
 * @property int $author 
 * @property string $type 
 * @property string $field_name 
 * @property string $rule 
 */
class WorkflowsModel extends \App\Model\Redmine\RedmineBaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'workflows';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['id' => 'integer', 'tracker_id' => 'integer', 'old_status_id' => 'integer', 'new_status_id' => 'integer', 'role_id' => 'integer', 'assignee' => 'integer', 'author' => 'integer'];
}
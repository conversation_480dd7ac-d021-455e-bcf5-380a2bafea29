<?php

declare (strict_types=1);

namespace App\Model\Redmine;

use App\Constants\TestPlanCode;
use Hyperf\Database\Model\SoftDeletes;
use Hyperf\DbConnection\Db;

/**
 * @property int $id
 * @property int $test_plan_case_id
 * @property int $test_status
 * @property string $remark
 * @property string $remark_html
 * @property string $detail
 * @property int $created_by
 * @property string $created_at
 * @property string $updated_at
 * @property string $deleted_at
 */
class TestPlanCaseResultModel extends RedmineBaseModel
{
    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';
    const DELETED_AT = 'deleted_at';
    use SoftDeletes;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'test_plan_case_result';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'test_plan_case_id',
        'test_status',
        'remark',
        'remark_html',
        'detail',
        'created_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'test_plan_case_id' => 'integer',
        'test_status' => 'integer',
        'detail' => 'json',
        'created_by' => 'integer'
    ];
    protected $appends = ['test_status_text'];

    public function createdUser(){
        return $this->hasOne('App\Model\Redmine\UserModel','id','created_by');
    }
    public function caseStepStep(){
        return $this->hasMany('App\Model\Redmine\TestPlanCaseStepModel','id','plan_case_result_id');
    }

    public function getTestStatusTextAttribute()
    {
        if (!empty($this->attributes['test_status'])) {
            return TestPlanCode::CASE_STATUS_MAP[$this->attributes['test_status']];
        }
    }
}
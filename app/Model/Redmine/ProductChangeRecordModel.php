<?php

declare (strict_types=1);
namespace App\Model\Redmine;

use Hyperf\Database\Model\SoftDeletes;

/**
 * @property int $id
 * @property string $title
 * @property array $notice_type
 * @property array $change_reason
 * @property array $change_impact
 * @property int $privacy_level
 * @property int $importance_level
 * @property array $notice_members
 * @property string $notice_content
 * @property string $notice_content_html
 * @property array $attachments
 * @property int $status
 * @property int $project_progress_id
 * @property int $created_by
 * @property string $created_at
 * @property string $updated_at
 * @property string $deleted_at
 */
class ProductChangeRecordModel extends RedmineBaseModel
{
    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';
    const DELETED_AT = 'deleted_at';
    use SoftDeletes;
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'product_change_record';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'notice_type',
        'change_reason',
        'change_impact',
        'privacy_level',
        'importance_level',
        'notice_members',
        'notice_content',
        'notice_content_html',
        'attachments',
        'status',
        'project_progress_id',
        'created_by'
    ];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'notice_type' => 'array',
        'change_reason' => 'array',
        'change_impact' => 'array',
        'privacy_level' => 'integer',
        'importance_level' => 'integer',
        'notice_members' => 'array',
        'attachments' => 'array',
        'status' => 'integer',
        'project_progress_id' => 'integer',
        'created_by' => 'integer'
    ];

    public function creator()
    {
        return $this->hasOne(UserModel::class,  'id', 'created_by');
    }

    public function good()
    {
        return $this->hasMany(ProductChangeRecordGoodModel::class, 'change_record_id', 'id');
    }

    public function project()
    {
        return $this->hasMany(ProductChangeRecordProjectModel::class, 'change_record_id', 'id');
    }
}
<?php

declare (strict_types=1);
namespace App\Model\Redmine;

use App\Core\Services\Product\ProductProgressService;
use Hyperf\Database\Model\SoftDeletes;

/**
 * @property int $id 
 * @property int $progress_id 
 * @property string $property 
 * @property string $prop_key 
 * @property string $old_value 
 * @property string $value 
 * @property string $created_at 
 * @property string $updated_at 
 * @property string $deleted_at 
 */
class ProductProgressDetailModel extends \App\Model\Redmine\RedmineBaseModel
{
    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';
    const DELETED_AT = 'deleted_at';

    use SoftDeletes;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'product_progress_details';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['progress_id', 'property', 'prop_key', 'old_value', 'value'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer', 'progress_id' => 'integer',
        'created_at' => 'datetime:Y-m-d H:i'
    ];

    protected $appends = [
        'prop_key_text',
        'diff_value_text',
    ];

    public function getPropKeyTextAttribute()
    {
        $value = CategoryModel::query()->where('type', 'product_desc_attr')->where('keywords', $this->attributes['prop_key'])->value('name');
        return $value ?? '';
    }

    public function getDiffValueTextAttribute()
    {
        return make(ProductProgressService::class)
            ->getDetailsAttr($this->attributes['property'], $this->attributes['prop_key'], $this->attributes['old_value'], $this->attributes['value']);
    }
}
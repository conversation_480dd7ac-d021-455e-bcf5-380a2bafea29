<?php

declare (strict_types=1);
namespace App\Model\Redmine;

/**
 * @property int $id 
 * @property int $project_id 
 * @property string $start_page 
 * @property int $status 
 */
class WikiModel extends \App\Model\Redmine\RedmineBaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'wikis';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['id' => 'integer', 'project_id' => 'integer', 'status' => 'integer'];

    public function wikiPage()
    {
        return $this->hasMany('\App\Model\Redmine\WikiPageModel', 'wiki_id', 'id');
    }

    public function wikiRedirects()
    {
        return $this->hasMany('\App\Model\Redmine\WikiRedirectModel', 'wiki_id', 'id');
    }

}
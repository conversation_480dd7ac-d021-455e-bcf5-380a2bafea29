<?php

declare (strict_types=1);
namespace App\Model\Redmine;

use Hyperf\Database\Model\SoftDeletes;

/**
 * @property int $id 
 * @property int $product_id 
 * @property string $name 
 * @property string $description 
 * @property int $status 
 * @property string $created_at 
 * @property string $updated_at 
 * @property string $deleted_at 
 */
class ProductMilestoneModel extends \App\Model\Redmine\RedmineBaseModel
{
    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';
    const DELETED_AT = 'deleted_at';
    use SoftDeletes;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'product_milestone';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['product_id', 'name', 'description', 'status', 'completed_on'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['id' => 'integer', 'product_id' => 'integer', 'status' => 'integer'];

    protected $appends = [
        'created_format'
    ];

    public function getCreatedFormatAttribute()
    {
        $value = '-';
        if (isset($this->attributes['created_at'])) {
            $value = date('Y/m/d', strtotime($this->attributes['created_at']));
        }
        return $value;
    }
}
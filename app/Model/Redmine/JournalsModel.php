<?php

declare (strict_types=1);
namespace App\Model\Redmine;

use App\Casts\JournalsNotesCasts;
use App\Core\Utils\TimeUtils;
use League\HTMLToMarkdown\HtmlConverter;
use Parsedown;

/**
 * @property int $id 
 * @property int $journalized_id 
 * @property string $journalized_type 
 * @property int $user_id 
 * @property string $notes 
 * @property \Carbon\Carbon $created_on 
 * @property int $private_notes 
 */
class JournalsModel extends \App\Model\Redmine\RedmineBaseModel
{
    public $timestamps = false;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'journals';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['journalized_id', 'journalized_type', 'user_id', 'notes', 'private_notes', 'created_on'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'journalized_id' => 'integer',
        'user_id' => 'integer',
        'created_on' => 'datetime',
        'private_notes' => 'integer',
        // 'notes' => JournalsNotesCasts::class,
    ];

    protected $appends = ['created_on_origin', 'created_on_reply'];

    /**
     * 关联详情表
     * @return \Hyperf\Database\Model\Relations\HasOne
     */
    public function details()
    {
        return $this->hasMany('\App\Model\Redmine\JournalDetailsModel', 'journal_id', 'id');
    }

    /**
     * 关联扩展表
     */
    public function ext()
    {
        return $this->hasOne('\App\Model\Redmine\JournalExtModel', 'journal_id', 'id');
    }

    public function userInfo()
    {
        return $this->hasOne('\App\Model\Redmine\UserModel', 'id', 'user_id');
    }

    public function issues()
    {
        return $this->hasOne('\App\Model\Redmine\IssueModel', 'id', 'journalized_id');
    }

    public function getCreatedOnOriginAttribute()
    {
        return !empty($this->attributes['created_on']) ? $this->attributes['created_on'] : '';
    }

    public function getCreatedOnReplyAttribute()
    {
        $value = '';
        if (!empty($this->attributes['created_on'])) {
            $dt = new \DateTime($this->attributes['created_on'],new \DateTimeZone('UTC'));
            $dt->setTimezone(new \DateTimeZone('Asia/Shanghai'));
            $value = TimeUtils::timeDisplay($dt->format('Y-m-d H:i:s'));
        }
        return $value;
    }

    public function getCreatedOnAttribute()
    {
        $value = '';
        if (!empty($this->attributes['created_on'])) {
            $dt = new \DateTime($this->attributes['created_on'],new \DateTimeZone('UTC'));
            $dt->setTimezone(new \DateTimeZone('Asia/Shanghai'));
            $value = $dt->format('Y-m-d H:i');
        }
        return $value;
    }

    // public function getNotesHtmlAttribute()
    // {
    //     $value = '';
    //     if (!empty($this->attributes['notes'])) {
    //         $value = Parsedown::instance()->parse($this->attributes['notes']);
    //         $value = htmlImgPos($value, $this->attributes['journalized_id'] ?? null, $this->attributes['journalized_type'] ?? null);
    //     }
    //     return $value;
    // }
}
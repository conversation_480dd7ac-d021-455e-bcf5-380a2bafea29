<?php

declare (strict_types=1);
namespace App\Model\Redmine;

use App\Core\Services\Project\IssueService;
use Parsedown;

/**
 * @property int $id 
 * @property int $journal_id 
 * @property string $property 
 * @property string $prop_key 
 * @property string $old_value 
 * @property string $value 
 */
class JournalDetailsModel extends \App\Model\Redmine\RedmineBaseModel
{
    public $timestamps = false;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'journal_details';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['journal_id', 'property', 'prop_key', 'old_value', 'value'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['id' => 'integer', 'journal_id' => 'integer'];

    protected $appends = ['property_text', 'prop_key_text', 'old_value_text', 'value_text'];

    public function getPropertyTextAttribute()
    {
        $value = '';
        if (!empty($this->attributes['property'])) {
            switch ($this->attributes['property']) {
                case 'attr':
                    $value = '属性';
                    break;
                case 'attachment':
                    $value = '附件';
                    break;
                case 'cf' :
                    $value = '自定义属性';
                break;
            }
        }
        return $value;
    }

    public function getPropKeyTextAttribute()
    {
        $value = '';
        if (!empty($this->attributes['prop_key']) && !empty($this->attributes['property'])) {
            switch ($this->attributes['property']) {
                case 'attr':
                    switch ($this->attributes['prop_key']) {
                        case 'tracker_id':
                            $value = '跟进类型';
                            break;
                        case 'project_id':
                            $value = '所属项目';
                            break;
                        case 'subject':
                            $value = '标题';
                            break;
                        case 'description':
                            $value = '描述';
                            break;
                        case 'due_date':
                            $value = '完成日期';
                            break;
                        case 'category_id':
                            $value = '分类';
                            break;
                        case 'status_id':
                            $value = '状态';
                            break;
                        case 'assigned_to_id':
                            $value = '指派给';
                            break;
                        case 'priority_id':
                            $value = '优先级';
                            break;
                        case 'fixed_version_id':
                            $value = '迭代';
                            break;
                        case 'author_id':
                            $value = '发起人';
                            break;
                        case 'lock_version':
                            $value = '锁定迭代';
                            break;
                        case 'start_date':
                            $value = '开始日期';
                            break;
                        case 'closed_on':
                            $value = '关闭日期';
                            break;
                        case 'parent_id':
                            $value = '父事项';
                            break;
                    }
                    break;
                case 'cf' :
                    $customField = CustomFieldsModel::query()->find($this->attributes['prop_key']);
                    $value = $customField->name ?? '';
                    break;
                case 'relation' :
                    $value = '关联' . make(IssueService::class)->getIssuetrackerName((int) $this->attributes['value']);
                    break;
            }
            $value = $value ?? ($this->attributes['prop_key'] ?? '');
        }
        return $value;
    }

    public function getOldValueTextAttribute()
    {
        return $this->getValueAttr($this->attributes['old_value'] ?? '', $this->attributes['prop_key'] ?? '', $this->attributes['property'] ?? '');
    }

    public function getValueTextAttribute()
    {
        return $this->getValueAttr($this->attributes['value'] ?? '', $this->attributes['prop_key'] ?? '', $this->attributes['property'] ?? '');
    }

    public function getValueAttr($value, $key, $prop)
    {
        $ret = '';
        if ($prop == 'cf') {
            $key = CustomFieldsModel::query()->where('id', $key)->value('name');
        }
        if (!empty($value) && !empty($key)) {
            switch ($key) {
                case 'tracker_id':
                    $ret = TrackersModel::query()->where('id', $value)->value('name');
                    break;
                case 'project_id':
                    $ret = ProjectModel::query()->where('id', $value)->value('name');
                    break;
                case 'status_id':
                    $ret = IssueStatusModel::query()->where('id', $value)->value('name');
                    break;
                case '销售跟进':
                case '多人指派':
                case '多人跟进':
                case 'author_id':
                case 'assigned_to_id':
                    $user = UserModel::query()->find($value);
                    if ($user) {
                        $ret = $user->lastname.$user->firstname;
                    }
                    break;
                case 'priority_id':
                    $ret = EnumerationModel::query()->where('id', $value)->value('name');
                    break;
                case 'fixed_version_id':
                case 'lock_version':
                    $ret = VersionModel::query()->where('id', $value)->value('name');;
                    break;
                case 'attachment' :
                    $attac = AttachmentModel::query()->where('id', $value)->first();
                    $ret = $attac ? $attac->url_attr : '';
                    break;
                case 'description' :
                    $ret = !empty($value) ?  Parsedown::instance()->parse($value) : '';
                    break;
                case 'relates':
                case 'parent_id':
                    $ret = IssueModel::query()->where('id', $value)->value('subject');
                    break;
                default:
                    $ret = $value ?? '';
            }
        }
        return $ret;
    }

}
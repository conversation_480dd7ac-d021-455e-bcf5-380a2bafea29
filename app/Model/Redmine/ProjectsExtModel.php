<?php

declare (strict_types=1);
namespace App\Model\Redmine;

use Hyperf\Cache\Annotation\Cacheable;
use Hyperf\Cache\Cache;

/**
 * @property int $project_id 
 * @property int $project_type
 * @property \Carbon\Carbon $created_on
 * @property \Carbon\Carbon $updated_on 
 * @property string $deleted_on 
 */
class ProjectsExtModel extends \App\Model\Redmine\RedmineBaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'projects_ext';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['project_id', 'project_type', 'product_id', 'icon', 'module',
        'relation_project_id', 'relation_product_id', 'issue_version_follow', 'issue_extend_watcher', 'batch_begin_time'
    ];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'project_id' => 'integer', 'created_on' => 'datetime', 'updated_on' => 'datetime',
        'module' => 'json', 'relation_project_id' => 'json', 'relation_product_id' => 'json',
        'issue_version_follow' => 'integer',
        'batch_begin_time' => 'datetime'
    ];

    protected $appends = [
        'project_type_text',
        'relation_project_names',
        'relation_product_names'
    ];


    // 获取关联项目的名字数组
    public function getRelationProjectNamesAttribute()
    {
        if (empty($this->attributes['relation_project_id']) ) {
            return [];
        }
        $ids = json_decode($this->attributes['relation_project_id'], true);
        $relatedProjects = $this->relatedProjectsNamesCache($ids);
        $names = [];
        $todo_issue_count = $this->todoIssuesCountCache($ids) ?? 0;
        $member_count = $this->memberCountCache($ids) ?? 0;
        $allIcon = $this->getIconCache($ids) ?? '';
        foreach ($ids as $id) {
            $relatedProjects[$id]['todo_issue_count'] = is_array($todo_issue_count) && isset($todo_issue_count[$id]) ? $todo_issue_count[$id] : 0;
            $relatedProjects[$id]['member_count'] = is_array($member_count) && isset($member_count[$id]) ? $member_count[$id] : 0;
            $relatedProjects[$id]['icon'] = is_array($allIcon) && isset($allIcon[$id]['icon']) ? $allIcon[$id]['icon'] : '';
            $names[] = $relatedProjects[$id] ?? '';
        }
        return $names;
    }


    public function todoIssuesCountCache($ids)
    {
        $counts = IssueModel::query(true)
            ->whereIn('status_id', make(IssueModel::class)->statusTodoProject)
            ->whereIn('project_id', $ids)
            ->groupBy('project_id')
            ->selectRaw('project_id, count(*) as count')
            ->pluck('count', 'project_id')
            ->all();
        return $counts;
    }

    public function memberCountCache($ids)
    {
        $counts = MemberModel::query(true)
            ->selectRaw('project_id, count(*) as count')
            ->whereIn('project_id', $ids)
            ->join('users', 'members.user_id', '=', 'users.id')
            ->where('users.status', '=', 1)
            ->groupBy('project_id')
            ->pluck('count', 'project_id')
            ->all();

        return $counts;
    }

    public function getIconCache($ids)
    {
        $icon = ProjectsExtModel::query(true)->selectRaw('icon, project_id')->whereIn('project_id', $ids)
            ->get()->keyBy('project_id')->toArray();
        return $icon;
    }

    public function relatedProjectsNamesCache($ids)
    {
//        return make(\App\Model\Redmine\ProjectModel::class)::all()->pluck(null, 'id')->toArray();
        return make(\App\Model\Redmine\ProjectModel::class)::query(true)->whereIn('id', $ids)->get()->keyBy('id')->toArray();

    }

    // 获取关联产品的名字
    public function getRelationProductNamesAttribute()
    {
        if (empty($this->attributes['relation_product_id'])) {
            return [];
        }
        $ids = json_decode($this->attributes['relation_product_id'], true);
        $relatedProducts = $this->relatedProductsNamesCache();
        $names = [];
        foreach ($ids as $id) {
            $names[] = $relatedProducts[$id] ?? '';
        }
        return $names;
    }

    // 获取Product 模型的 name 字段
    /**
     * @Cacheable(prefix="all_relation_product_names_attribute_", ttl=10)
     */
    public function relatedProductsNamesCache()
    {
        return make(\App\Model\Redmine\ProjectModel::class)::query()
            ->select('id', 'name')
            ->pluck('name', 'id')
            ->all();
    }

    public function getProjectTypeTextAttribute()
    {
        $attr = '';
        if (!empty($this->attributes['project_type'])) {
//            $attr = \App\Model\Redmine\CategoryModel::query()->where('keywords', $this->attributes['project_type'])->value('name');
            $typeCollect = $this->projectTypeTextAttributeCache();
            $attr = $typeCollect[$this->attributes['project_type']] ?? '';
        }
        return $attr;
    }

    /**
     * @Cacheable(prefix="all_prject_type_text_attribute_", ttl=10)
     */
    public function projectTypeTextAttributeCache()
    {
//        return  \App\Model\Redmine\CategoryModel::query()->where('keywords',$type)->value('name');
        return  CategoryModel::query()
            ->select('name', 'keywords')
            ->where('type', 'projects_type')
            ->pluck('name', 'keywords')
            ->all();
    }


}
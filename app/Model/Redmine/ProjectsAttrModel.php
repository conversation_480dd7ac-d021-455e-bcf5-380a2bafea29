<?php

declare (strict_types=1);
namespace App\Model\Redmine;

use Hyperf\Database\Model\SoftDeletes;

/**
 * @property int $id 
 * @property int $project_id 
 * @property int $attr_id 
 * @property int $attr_value_id 
 * @property string $url 
 * @property string $created_at 
 * @property string $updated_at 
 * @property string $deleted_at 
 */
class ProjectsAttrModel extends \App\Model\Redmine\RedmineBaseModel
{
    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'projects_attr';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['project_id', 'attr_id', 'attr_value_id'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['id' => 'integer', 'project_id' => 'integer', 'attr_id' => 'integer', 'attr_value_id' => 'json'];

    // protected $appends = [
    //     'attr_text',
    // ];
    //
    // public function getAttrTextAttribute()
    // {
    //     $value = '';
    //     if (!empty($this->attributes['attr_id'])) {
    //         return CategoryModel::query(true)->where('id', $this->attributes['attr_id'])->value('name');
    //     }
    //     return $value;
    // }

}
<?php

declare (strict_types=1);
namespace App\Model\Marketing;

use App\Constants\DataBaseCode;
use Hyperf\Database\Model\SoftDeletes;
/**
 * @property int $id 
 * @property int $platform_id 
 * @property string $platform_name 平台名称
 * @property string $name 
 * @property int $is_official 
 * @property int $user_count 用户数量
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 * @property string $deleted_at 
 */
class MarketingChannel extends \App\Model\Model
{
    use SoftDeletes;

    protected $connection = DataBaseCode::STATIONPC_MANAGERCN;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'marketing_channel';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['id' => 'integer', 'platform_id' => 'integer', 'is_official' => 'integer', 'user_count' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];
}
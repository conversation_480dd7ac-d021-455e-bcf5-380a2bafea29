<?php

declare (strict_types=1);
namespace App\Model\TchipSale;

/**
 * @property int $id 
 * @property string $keyword 
 * @property string $vals 
 * @property string $opts 
 * @property string $types 
 * @property string $name 
 * @property string $notes 
 * @property int $gid 
 * @property int $sys 
 * @property int $sort 
 */
class ConfigModel extends \App\Model\TchipSale\SaleBaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'config';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['id' => 'integer', 'gid' => 'integer', 'sys' => 'integer', 'sort' => 'integer'];
}
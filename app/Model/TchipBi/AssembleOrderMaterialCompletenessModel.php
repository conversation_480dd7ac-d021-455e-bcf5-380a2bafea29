<?php

declare (strict_types=1);

namespace App\Model\TchipBi;

use App\Constants\AssembleOrderCode;
use App\Constants\ProductionCode;
use App\Model\ProductionModel;
use Carbon\Carbon;
use Hyperf\Database\Model\SoftDeletes;

/**
 * @property int $id
 * @property int $assemble_order_id
 * @property int $type
 * @property int $status
 * @property string $attachment_ids
 * @property string $images
 * @property string $remark
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property string $deleted_at
 * @property int $audit_status
 * @property int $audit_user_id
 */
class AssembleOrderMaterialCompletenessModel extends ProductionModel
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'assemble_order_material_completeness';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'assemble_order_id',
        'type',
        'status',
        'attachment_ids',
        'images',
        'remark',
        'created_at',
        'updated_at',
        'deleted_at',
        'audit_status',
        'audit_user_id',
    ];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id'                => 'integer',
        'assemble_order_id' => 'integer',
        'attachment_ids'    => 'array',
        'images'    => 'array',
        'type'              => 'integer',
        'status'            => 'integer',
        'created_at'        => 'datetime',
        'updated_at'        => 'datetime',
        'audit_status'      => 'integer',
        'audit_user_id'     => 'integer',
    ];
    //日志变更类型，用于前端显示
    protected $changeCategory = AssembleOrderCode::LOG_CHANGE_CATEGORY_COMPLETENESS;
    protected $logField = [
        [
            'field' => 'status',
            'field_text' => '齐料状态',
            'property' => self::PROPERTY_OPTION,
        ],
        [
            'field' => 'attachment_ids',
            'field_text' => '附件',
            'property' => self::PROPERTY_ATTACHMENT_IDS,
        ],
        [
            'field' => 'images',
            'field_text' => '图片',
            'property' => self::PROPERTY_IMAGES,
        ],
        [
            'field' => 'audit_status',
            'field_text' => '审核状态',
            'property' => self::PROPERTY_OPTION,
        ],
        [
            'field' => 'audit_user_id',
            'field_text' => '审核人',
            'property' => self::PROPERTY_USER_ID,
        ],
        [
            'field' => 'remark',
            'field_text' => '备注',
            'property' => self::PROPERTY_TEXT,
        ],
    ];

    function setSelfFieldValueText(array $fields): array
    {
        foreach ($fields as &$field) {
            if (empty($field['property'])) {
                continue;
            }
            if ($field['property'] == self::PROPERTY_OPTION) {
                switch ($field['field']) {
                    case 'status':
                        $field['old_value_text'] = AssembleOrderCode::MATERIAL_STATUS[$field['old_value']] ?? '';
                        $field['new_value_text'] = AssembleOrderCode::MATERIAL_STATUS[$field['new_value']] ?? '';
                        break;
                    case 'audit_status':
                        $field['old_value_text'] = AssembleOrderCode::ATTACHMENT_AUDIT_STATUS[$field['old_value']] ?? '';
                        $field['new_value_text'] = AssembleOrderCode::ATTACHMENT_AUDIT_STATUS[$field['new_value']] ?? '';
                        break;
                    default :
                        break;
                }
            }
        }
//        var_dump($fields);
        return $fields;
    }

    protected function setChangeCategory(&$params)
    {
        $attributes = $this->getAttributes();
        $type = $attributes['type'] ?? 0;
        $params['change_category'] = $this->changeCategory;
        $params['change_category_tag'] = AssembleOrderCode::MATERIAL_TYPE[$type] ?? '';
    }
}
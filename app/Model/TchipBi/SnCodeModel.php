<?php

declare (strict_types=1);

namespace App\Model\TchipBi;

use Hyperf\Database\Model\SoftDeletes;

/**
 * @property int $id
 * @property int $origin_type
 * @property string $code
 * @property string $used_no
 * @property int $used_type
 * @property int $used_relate_id
 * @property int $used_user_id
 * @property string $used_product
 * @property string $used_product_code
 * @property string $used_client
 * @property string $used_date
 * @property int $is_used
 * @property string $sort
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property string $deleted_at
 */
class SnCodeModel extends \App\Model\Model
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'sn_code';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'origin_type',
        'sn_code',
        'used_no',
        'used_type',
        'used_relate_id',
        'used_user_id',
        'used_product',
        'used_product_code',
        'used_client',
        'used_date',
        'is_used',
        'sort',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id'             => 'integer',
        'used_type'      => 'integer',
        'used_relate_id' => 'integer',
        'used_user_id'   => 'integer',
        'is_used'        => 'integer',
        'created_at'     => 'datetime',
        'updated_at'     => 'datetime'
    ];
    public function getSnCodeAttribute($value)
    {
        return strtoupper($value);  // 将字段值转换为大写
    }
}
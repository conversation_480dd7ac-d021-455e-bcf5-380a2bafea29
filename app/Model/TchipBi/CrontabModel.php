<?php

declare (strict_types=1);

namespace App\Model\TchipBi;

use App\Constants\CrontabCode;
use Hyperf\Database\Model\SoftDeletes;

/**
 * @property int            $id
 * @property string         $type
 * @property string         $title
 * @property string         $class
 * @property string         $method
 * @property string         $content
 * @property string         $schedule
 * @property int            $status
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Carbon\Carbon $begintime
 * @property \Carbon\Carbon $endtime
 * @property \Carbon\Carbon $executetime
 */
class CrontabModel extends \App\Model\Model
{

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'crontab';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id', 'type', 'title', 'class', 'method', 'schedule', 'created_at',
        'updated_at', 'begintime', 'endtime', 'executetime', 'weigh', 'status',
        'run_env', 'run_status'
    ];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id'          => 'integer',
        'weigh'       => 'integer',
        'status'      => 'integer',
        'created_at'  => 'datetime',
        'updated_at'  => 'datetime',
        'begintime'   => 'datetime',
        'endtime'     => 'datetime',
        'executetime' => 'datetime',
        'run_env'     => 'json',
        'run_status'  => 'integer',
    ];

    protected $appends = [
        'status_text',
        'run_status_text'
    ];

    public $runStatusList = [
        CrontabCode::RUN_STATUS_NONE => '未执行',
        CrontabCode::RUN_STATUS_DONE => '执行完成',
        CrontabCode::RUN_STATUS_RUN => '执行中',
        CrontabCode::RUN_STATUS_FAIL => '执行失败',
    ];

    public function getStatusTextAttribute()
    {
        return $this->attributes['status'] == 1 ? '正常' : '停止';
    }

    public function getRunStatusTextAttribute()
    {
        $value = '';
        if (!empty($this->attributes['run_status']) && !empty($this->runStatusList[$this->attributes['run_status']])) {
            $value = $this->runStatusList[$this->attributes['run_status']];
        }
        return $value;
    }
}
<?php

declare (strict_types=1);

namespace App\Model\TchipBi;

use App\Model\Model;
use Carbon\Carbon;
use Hyperf\Database\Model\SoftDeletes;

/**
 * @property int $id
 * @property string $scene_type
 * @property int $old_status_id
 * @property int $new_status_id
 * @property int $create_user_id
 * @property int $is_send_notice
 * @property int $pre_operation
 * @property int $is_reverse
 * @property string $condition
 * @property string $formula
 * @property string $description
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property string $deleted_at
 */
class WorkFlowModel extends Model
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'work_flow';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'scene_type',
        'old_status_id',
        'new_status_id',
        'create_user_id',
        'is_send_notice',
        'pre_operation',
        'is_reverse',
        'condition',
        'formula',
        'level',
        'description',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'old_status_id' => 'integer',
        'new_status_id' => 'integer',
        'create_user_id' => 'integer',
        'pre_operation' => 'array',
        'is_reverse' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
}
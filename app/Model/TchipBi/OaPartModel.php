<?php

declare (strict_types=1);
namespace App\Model\TchipBi;

use Hyperf\Database\Model\SoftDeletes;

/**
 * @property int $id 
 * @property int $parent_id 
 * @property int $company_id 
 * @property string $name 
 * @property int $status 
 * @property int $access 
 * @property string $comment 
 * @property int $sort 
 */
class OaPartModel extends \App\Model\Model
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'oa_part';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['parent_id', 'company_id', 'name', 'status', 'sort'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['id' => 'integer', 'parent_id' => 'integer', 'company_id' => 'integer', 'status' => 'integer', 'access' => 'integer', 'sort' => 'integer'];

    /**
     * 关联公司表
     */
    public function userFile()
    {
        return $this->hasMany('\App\Model\TchipBi\OaUserFilesModel', 'part_id', 'id');
    }
}
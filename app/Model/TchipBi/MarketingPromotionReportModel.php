<?php

declare (strict_types=1);
namespace App\Model\TchipBi;

use Hyperf\Database\Model\SoftDeletes;
/**
 * @property int $id 
 * @property string $brand_name 
 * @property string $brand_code 
 * @property int $platform_id 
 * @property int $users_count 
 * @property int $interact_count 
 * @property int $exposure_count 
 * @property float $click_rate
 * @property float $click_conversion_rate
 * @property float $invest_return_rate
 * @property string $expect_amount 
 * @property string $practical_amount 
 * @property int $expect_display 
 * @property int $practical_display 
 * @property int $expect_click 
 * @property int $practical_click 
 * @property int $expect_relay 
 * @property int $practical_relay 
 * @property int $expect_invest_return 
 * @property int $practical_invest_return 
 * @property int $expect_users 
 * @property int $practical_users 
 * @property int $expect_interact 
 * @property int $practical_interact 
 * @property int $expect_exposure 
 * @property int $practical_exposure
 * @property int $stars
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 * @property string $deleted_at 
 */
class MarketingPromotionReportModel extends \App\Model\Model
{
    use SoftDeletes;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'marketing_promotion_report';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'brand_name', 'brand_code', 'platform_id', 'date', 'users_count', 'interact_count', 'exposure_count', 'click_rate',
        'click_conversion_rate', 'invest_return_rate', 'expect_amount', 'practical_amount', 'expect_display',
        'practical_display', 'expect_click', 'practical_click', 'expect_relay', 'practical_relay', 'expect_invest_return',
        'practical_invest_return', 'expect_users', 'practical_users', 'expect_interact', 'practical_interact',
        'expect_exposure', 'practical_exposure', 'created_at', 'updated_at', 'expect_ctr', 'practical_ctr','stars'
    ];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer', 'platform_id' => 'integer', 'users_count' => 'integer', 'interact_count' => 'integer', 'exposure_count' => 'integer',
        'click_rate' => 'float',
        'click_conversion_rate' => 'float',
        'invest_return_rate' => 'float',
        'expect_invest_return' => 'float',
        'practical_invest_return' => 'float',
        'expect_display' => 'integer',
        'practical_display' => 'integer',
        'expect_click' => 'integer',
        'practical_click' => 'integer',
        'expect_users' => 'integer',
        'practical_users' => 'integer',
        'expect_interact' => 'integer',
        'practical_interact' => 'integer',
        'expect_exposure' => 'integer',
        'practical_exposure' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
}
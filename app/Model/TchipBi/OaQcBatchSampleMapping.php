<?php

declare(strict_types=1);

namespace App\Model\TchipBi;

use Hyperf\Database\Model\SoftDeletes;
/**
 * @property int $id 
 * @property int $inspection_level_id 
 * @property int $min_num 
 * @property int $max_num 
 * @property string $sample_code 
 * @property int $sample_size 
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 * @property \Carbon\Carbon|null $deleted_at 
 */
class OaQcBatchSampleMapping extends \App\Model\Model
{
    use SoftDeletes;
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'oa_qc_batch_sample_mapping';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'inspection_level_id', 
        'min_num', 
        'max_num', 
        'sample_code', 
        'sample_size', 
        'created_at', 
        'updated_at', 
        'deleted_at'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'inspection_level_id' => 'integer',
        'min_num' => 'integer',
        'max_num' => 'integer',
        'sample_size' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];
}
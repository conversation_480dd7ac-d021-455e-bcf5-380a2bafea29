<?php

declare (strict_types=1);

namespace App\Model\TchipBi;

use App\Constants\AssembleOrderCode;
use App\Constants\ProductionCode;
use App\Model\ProductionModel;
use Carbon\Carbon;
use Hyperf\Database\Model\SoftDeletes;

/**
 * @property int $id
 * @property int $assemble_order_id
 * @property string $stock_order_code
 * @property string $predict_online_date
 * @property string $actual_online_date
 * @property int $order_type
 * @property int $assemble_type
 * @property int $assemble_user_id
 * @property int $software_user_id
 * @property int $order_user_id
 * @property int $test_user_id
 * @property int $shipment_place
 * @property int $mac_sn_attachment_id
 * @property int $mac_text_origin_type
 * @property int $sn_text_origin_type
 * @property string $sn_no_text
 * @property string $mac_address_text
 * @property int $is_complete
 * @property string $attachment_ids
 * @property string $material_remark
 * @property string $other_remark
 * @property int $approve_status
 * @property int $material_status
 * @property int $completeness_status
 * @property int $assemble_data_status
 * @property int $soft_data_status
 * @property int $first_assemble_data_status
 * @property int $first_soft_data_status
 * @property int $summary_finish_status
 * @property int $product_finished_data_status
 * @property int $product_soft_data_status
 * @property int $ship_data_status
 * @property string $approve_date
 * @property string $predict_delivery_date
 * @property int $work_status_id
 * @property int $pre_assemble_order_id
 * @property string $pcba_remark
 * @property string $sn_remark
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property string $deleted_at
 * @property int $mac_range_origin_type
 * @property int $sn_range_origin_type
 * @property string $sn_no_range
 * @property string $mac_address_range
 * @property string $relate_order_code
 */
class AssembleOrderInfoModel extends ProductionModel
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'assemble_order_info';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'assemble_order_id',
        'stock_order_code',
        'actual_online_date',
        'predict_online_date',
        'order_type',
        'assemble_type',
        'assemble_user_id',
        'software_user_id',
        'order_user_id',
        'test_user_id',
        'shipment_place',
        'mac_sn_attachment_id',
        'mac_text_origin_type',
        'sn_text_origin_type',
        'sn_no_text',
        'mac_address_text',
        'is_complete',
        'attachment_ids',
        'material_remark',
        'material_status',
        'completeness_status',
        'assemble_data_status',
        'soft_data_status',
        'first_assemble_data_status',
        'first_soft_data_status',
        'product_finished_data_status',
        'product_soft_data_status',
        'ship_data_status',
        'other_remark',
        'approve_status',
        'summary_finish_status',
        'approve_date',
        'predict_delivery_date',
        'work_status_id',
        'pre_assemble_order_id',
        'pcba_remark',
        'sn_remark',
        'created_at',
        'updated_at',
        'deleted_at',
        'mac_range_origin_type',
        'sn_range_origin_type',
        'sn_no_range',
        'mac_address_range',
        'relate_order_code',
    ];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id'                    => 'integer',
        'assemble_order_id'     => 'integer',
        'order_type'            => 'integer',
        'assemble_type'         => 'integer',
        'assemble_user_id'      => 'integer',
        'software_user_id'      => 'integer',
        'order_user_id'      => 'integer',
        'test_user_id'      => 'integer',
        'is_complete'           => 'integer',
        'approve_status'        => 'integer',
        'material_status'       => 'integer',
        'completeness_status'        => 'integer',
        'assemble_data_status'       => 'integer',
        'soft_data_status'           => 'integer',
        'first_assemble_data_status' => 'integer',
        'first_soft_data_status'     => 'integer',
        'summary_finish_status' => 'integer',
        'work_status_id'        => 'integer',
        'pre_assemble_order_id' => 'integer',
        'shipment_place'        => 'integer',
        'attachment_ids'        => 'array',
        'mac_address_range'        => 'array',
        'sn_no_range'        => 'array',
        // 原始数据类型为json，用array来接收
        'created_at'            => 'datetime',
        'updated_at'            => 'datetime'
    ];

    //日志变更类型，用于前端显示
    protected $changeCategory = AssembleOrderCode::LOG_CHANGE_CATEGORY_INFO;
    protected $logField = [
        [
            'field' => 'order_type',
            'field_text' => '订单类型',
            'property'   => self::PROPERTY_OPTION,
        ],
        [
            'field' => 'assemble_type',
            'field_text' => '组装类型',
            'property'   => self::PROPERTY_OPTION,
        ],
        [
            'field' => 'assemble_user_id',
            'field_text' => '组装负责人',
            'property'   => self::PROPERTY_USER_ID,
        ],
        [
            'field' => 'software_user_id',
            'field_text' => '软件负责人',
            'property'   => self::PROPERTY_USER_ID,
        ],
        [
            'field' => 'test_user_id',
            'field_text' => '测试负责人',
            'property'   => self::PROPERTY_USER_ID,
        ],
        [
            'field' => 'shipment_place',
            'field_text' => '出货地点',
            'property'   => self::PROPERTY_OPTION,
        ],
        [
            'field' => 'sn_no_text',
            'field_text' => 'sn号文本',
            'property'   => self::PROPERTY_TEXT,
        ],
        [
            'field' => 'mac_address_text',
            'field_text' => 'mac地址文本',
            'property'   => self::PROPERTY_TEXT,
        ],
        [
            'field' => 'is_complete',
            'field_text' => '是否完善',
            'property'   => self::PROPERTY_OPTION,
        ],
        [
            'field' => 'predict_delivery_date',
            'field_text' => '预定交货时间',
            'property'   => self::PROPERTY_STRING,
        ],
        [
            'field' => 'actual_online_date',
            'field_text' => '实际上线时间',
            'property'   => self::PROPERTY_STRING,
        ],
        [
            'field' => 'predict_online_date',
            'field_text' => '预计上线时间',
            'property'   => self::PROPERTY_STRING,
        ],
        [
            'field' => 'attachment_ids',
            'field_text' => '附件',
            'property'   => self::PROPERTY_ATTACHMENT_IDS,
        ],
        [
            'field' => 'material_remark',
            'field_text' => '材料情况',
            'property'   => self::PROPERTY_TEXT,
        ],
        [
            'field' => 'other_remark',
            'field_text' => '客人备注',
            'property'   => self::PROPERTY_TEXT,
        ],
        [
            'field' => 'summary_finish_status',
            'field_text' => '总结完成状态',
            'property'   => self::PROPERTY_OPTION,
        ],
        [
            'field' => 'approve_status',
            'field_text' => '核准状态',
            'property'   => self::PROPERTY_OPTION,
        ],
        [
            'field' => 'approve_date',
            'field_text' => '核准日期',
            'property'   => self::PROPERTY_STRING,
        ],
        [
            'field' => 'work_status_id',
            'field_text' => '订单阶段',
            'property'   => self::PROPERTY_WORK_STATUS_ID,
        ],
        [
            'field' => 'mac_sn_attachment_id',
            'field_text' => 'MAC/SN导入附件',
            'property'   => self::PROPERTY_ATTACHMENT_ID,
        ],
        [
            'field' => 'mac_text_origin_type',
            'field_text' => 'MAC文本来源类型',
            'property'   => self::PROPERTY_OPTION,
        ],
        [
            'field' => 'sn_text_origin_type',
            'field_text' => 'SN文本来源类型',
            'property'   => self::PROPERTY_OPTION,
        ],
        [
            'field' => 'sn_remark',
            'field_text' => '整机SN',
            'property'   => self::PROPERTY_TEXT,
        ],
        [
            'field' => 'pcba_remark',
            'field_text' => 'PCBA',
            'property'   => self::PROPERTY_TEXT,
        ],
        [
            'field' => 'completeness_status',
            'field_text' => '备料状态',
            'property'   => self::PROPERTY_OPTION,
        ],
        [
            'field' => 'assemble_data_status',
            'field_text' => '组装资料状态',
            'property'   => self::PROPERTY_OPTION,
        ],
        [
            'field' => 'soft_data_status',
            'field_text' => '软件资料状态',
            'property'   => self::PROPERTY_OPTION,
        ],
        [
            'field' => 'first_assemble_data_status',
            'field_text' => '首件组装资料状态',
            'property'   => self::PROPERTY_OPTION,
        ],
        [
            'field' => 'first_soft_data_status',
            'field_text' => '首件软件资料状态',
            'property'   => self::PROPERTY_OPTION,
        ],
        [
            'field' => 'product_finished_data_status',
            'field_text' => '生产成品资料状态',
            'property'   => self::PROPERTY_OPTION,
        ],
        [
            'field' => 'product_soft_data_status',
            'field_text' => '生产软件资料状态',
            'property'   => self::PROPERTY_OPTION,
        ],
        [
            'field' => 'relate_order_code',
            'field_text' => '关联订单号',
            'property'   => self::PROPERTY_STRING,
        ],
        [
            'field' => 'mac_range_origin_type',
            'field_text' => 'MAC区间来源类型',
            'property'   => self::PROPERTY_OPTION,
        ],
        [
            'field' => 'sn_range_origin_type',
            'field_text' => 'SN区间来源类型',
            'property'   => self::PROPERTY_OPTION,
        ],
        [
            'field' => 'mac_address_range',
            'field_text' => 'MAC区间',
            'property'   => self::PROPERTY_TEXT,
        ],
        [
            'field' => 'sn_no_range',
            'field_text' => 'SN区间',
            'property'   => self::PROPERTY_TEXT,
        ],
        [
            'field'      => 'deleted_at',
            'field_text' => '删除时间',
            'property'   => self::PROPERTY_STRING,
        ],
    ];


    public function setSelfFieldValueText(array $fields): array
    {
        foreach ($fields as &$field) {
            if (empty($field['property'])) {
                continue;
            }
            if ($field['property'] == self::PROPERTY_OPTION) {
                switch ($field['field']) {
                    case 'order_type':
                        $option = AssembleOrderCode::ORDER_TYPE;
                        break;
                    case 'assemble_type':
                        $option = AssembleOrderCode::ASSEMBLE_TYPE;
                        break;
                    case 'shipment_place':
                        $option = AssembleOrderCode::SHIPMENT_PLACE;
                        break;
                    case 'is_complete':
                        $option = AssembleOrderCode::IS_COMPLETE;
                        break;
                    case 'summary_finish_status':
                        $option = AssembleOrderCode::SUMMARY_STATUS;
                        break;
                    case 'mac_text_origin_type':
                    case 'sn_text_origin_type':
                    case 'mac_range_origin_type':
                    case 'sn_range_origin_type':
                        $option = ProductionCode::ORIGIN_TYPE;
                    break;
                    case 'completeness_status':
                        $option = AssembleOrderCode::COMPLETENESS_STATUS;
                        break;
                    case 'soft_data_status':
                    case 'first_assemble_data_status':
                    case 'first_soft_data_status':
                    case 'product_soft_data_status':
                    case 'product_finished_data_status':
                        $option = AssembleOrderCode::DATA_STATUS;
                        break;
                    case 'assemble_data_status':
                        $option = AssembleOrderCode::DATA_COMPLETE_STATUS;
                        break;
                    case 'approve_status':
                        $option = AssembleOrderCode::APPROVE_STATUS;
                        break;
                    default :
                        $option = [];
                        break;
                }
                $field['old_value_text'] = $option[$field['old_value']] ?? '';
                $field['new_value_text'] = $option[$field['new_value']]?? '';
            }
        }
        return $fields;
    }
}
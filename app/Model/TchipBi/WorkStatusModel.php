<?php

declare (strict_types=1);
namespace App\Model\TchipBi;

use Hyperf\Database\Model\SoftDeletes;
/**
 * @property int $id 
 * @property string $scene_type 
 * @property string $name 
 * @property string $key 
 * @property int $sort 
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 * @property string $deleted_at 
 */
class WorkStatusModel extends \App\Model\Model
{
    use SoftDeletes;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'work_status';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['id', 'scene_type', 'name', 'key', 'sort', 'created_at', 'updated_at', 'deleted_at'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['id' => 'integer', 'sort' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];
}
<?php

declare (strict_types=1);
namespace App\Model\TchipBi;

use Hyperf\Database\Model\SoftDeletes;
/**
 * @property int $id 
 * @property int $user_id 
 * @property string $workwx_userid 
 * @property string $template_id 
 * @property string $approver 
 * @property string $notifyer 
 * @property string $material 
 * @property string $prod_name 
 * @property int $num 
 * @property string $lend_date 
 * @property int $status 
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 * @property string $deleted_at 
 */
class OaProductBorrowModel extends \App\Model\Model
{
    use SoftDeletes;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'oa_product_borrow';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'workwx_userid',
        'template_id',
        'approver',
        'notifyer',
        'prod_code',
        'prod_name',
        'prod_count',
        'borrow_date',
        'return_date',
        'sp_status',
        'borrow_status',
        'sp_no',
        'approver_details',
        'approver_attr',
        'remark'
    ];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer', 'user_id' => 'integer', 'num' => 'integer', 'approver' => 'json',
        'notifyer' => 'json', 'approver_details' => 'json',
        'status' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime',
        'borrow_date' =>'datetime:Y-m-d'
    ];

    protected $appends = [
        'approver_user',
        'notifyer_user',
        'sp_status_text',
        'borrow_status_text',
    ];

    protected $borrowStatusList = [
        0 => '已关闭',
        1 => '已归还',
        2 => '申请中',
        3 => '借用中'
    ];

    protected $spStatusList = [
        -1 => '未提交',
        1  => '审批中',
        2  => '已通过',
        3  => '已驳回',
        4  => '已撤销',
        6  => '通过后撤销',
        7  => '已删除',
        10 => '已支付',
    ];

    public function user()
    {
        return $this->belongsTo('\App\Model\TchipBi\UserModel', 'user_id', 'id');
    }

    public function children()
    {
        return $this->hasMany('\App\Model\TchipBi\OaProductBorrowDetailsModel', 'b_id', 'id');
    }

    public function getApproverUserAttribute()
    {
        $value = [];
        if (!empty($this->attributes['approver'])) {
            $value = UserModel::query()->select(['id','name','biz_mail'])->whereIn('id', is_array($this->attributes['approver']) ? $this->attributes['approver'] : json_decode($this->attributes['approver']))->get();
        }
        return $value;
    }

    public function getNotifyerUserAttribute()
    {
        $value = [];
        if (!empty($this->attributes['notifyer'])) {
            $value = UserModel::query()->select(['id','name','biz_mail'])->whereIn('id', is_array($this->attributes['notifyer']) ? $this->attributes['notifyer'] : json_decode($this->attributes['notifyer']))->get();
        }
        return $value;
    }

    public function getSpStatusTextAttribute()
    {
        $value = '未知状态';
        if (isset($this->attributes['sp_status'])) {
            $value = $this->getSpStatusText($this->attributes['sp_status']);
        }
        return $value;
    }

    public function getBorrowStatusTextAttribute()
    {
        $value = '未知状态';
        if (isset($this->attributes['borrow_status'])) {
            $value = $this->getBorrowStatusText($this->attributes['borrow_status']);
        }
        return $value;
    }

    public function getSpStatusList()
    {
        $rows = [];
        foreach ($this->spStatusList as $key => $value) {
            $rows[] = [
                'id' => $key,
                'name' => $value,
            ];
        }
        return $rows;
    }

    public function getBorrowStatusList()
    {
        $rows = [];
        foreach ($this->borrowStatusList as $key => $value) {
            $rows[] = [
                'id' => $key,
                'name' => $value,
            ];
        }
        return $rows;
    }

    public function getSpStatusText($status)
    {
        return $this->spStatusList[$status] ?? '未知状态';
    }

    public function getBorrowStatusText($status)
    {
        return $this->borrowStatusList[$status] ?? '未知状态';
    }
}
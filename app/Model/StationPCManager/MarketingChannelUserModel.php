<?php

declare (strict_types=1);
namespace App\Model\StationPCManager;

use App\Constants\DataBaseCode;
use App\Model\Model;

/**
 * StationPC-营销模块
 */
class MarketingChannelUserModel extends ManagerBaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'marketing_channel_user';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['channel_id', 'user_count', 'user_increase'];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['createtime' => 'datetime:Y-m-d',
                        'updatetime' => 'datetime:Y-m-d'];

    protected $appends = [];

    public function channel()
    {
        return $this->belongsTo('\App\Model\StationPCManager\MarketingChannelModel', 'channel_id', 'id');
    }


}
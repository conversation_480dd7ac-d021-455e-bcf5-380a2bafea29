<?php

declare(strict_types=1);

namespace App\Command;

use App\Model\TchipSale\LinkageModel;
use App\Core\Services\TchipSale\ErpService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Psr\Container\ContainerInterface;
use Symfony\Component\Console\Input\InputOption;


/**
 * @Command
 */
class TestCommand extends HyperfCommand
{
    /**
     * 执行的命令行
     *
     * @var string
     */
    protected $name = 'testcommand:command';

    /**
     * @var ContainerInterface
     */
    protected $container;

    protected $erpService;

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;

        parent::__construct('testcommand:testcommand');

        $this->erpService = make(ErpService::class);
    }

    public function configure()
    {
        parent::configure();
        $this->setDescription('testcommand');
        $this->addOption('dated', 'N', InputOption::VALUE_REQUIRED, '日期');
    }

    /**
     * @param date $dated 日期格式:2022.07.01-2022.07.19
     * @return void
     */
    public function handle()
    {
        // $result = $this->erpService->syncErpProductStatus();
        $result = $this->erpService->statisticsByDay();
        $this->line("完成", 'info');
    }
}

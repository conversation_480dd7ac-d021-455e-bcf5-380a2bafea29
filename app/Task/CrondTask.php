<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date      2022/8/6 下午4:43
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Task;

use App\Core\Utils\Log;
use App\Exception\AppException;
use App\Model\TchipBi\CrontabModel;
use Hyperf\Crontab\Annotation\Crontab;
use Poliander\Cron\CronExpression;

/**
 * 检查任务列表
 * @Crontab(name="CrondTask", rule="* * * * *",callback="execute", memo="每分钟检查")
 */
class CrondTask
{
    /**
     * @return void
     * @throws \Exception
     */
    public function execute()
    {
        $env = env('APP_ENV', '');
        Log::get('system', 'system')->info('==============================================================================');
        Log::get('system', 'system')->info('检查任务列表');

        $dt = new \DateTime(date('Y-m-d H:i', time()));
        $crondArr = CrontabModel::where('status', 1)->get()->toArray();

        Log::get('system', 'system')->info("当前环境:[{$env}]");
        if (count($crondArr) == 0) {
            Log::get('system', 'system')->info("当前没有可执行的任务");
        }
        foreach ($crondArr as $crond) {
            $runEnv = !empty($crond['run_env']) ? $crond['run_env'] : ['product'];
            // Log::get('system', 'system')->info("本任务可以运行的环境", $runEnv);
            if (!in_array($env, $runEnv)) {
                Log::get('system', 'system')->info("任务[{$crond['title']}],不在运行环境,跳过任务");
                continue;
            }
            if ($crond['class'] && class_exists($crond['class'])) {
                if (method_exists($crond['class'], $crond['method'])) {
                    $class = $crond['class'];
                    $method = $crond['method'];
                    $schedule = $crond['schedule'];
                    $crondId = $crond['id'];

                    co(function () use ($class, $method, $schedule, $dt, $crondId) {
                        try {
                            $expression = new CronExpression($schedule);
                            $isMatching = $expression->isMatching($dt);
                            if ($isMatching) {
                                $services = new $class();
                                CrontabModel::where('id', $crondId)->update(['run_status' => 2]);
                                $services->$method();
                                CrontabModel::where('id', $crondId)->update(['executetime' => date('Y-m-d H:i:s'), 'run_status' => 1]);
                            } else {
                                //Log::get('system', 'system')->info('not matching');
                            }
                        } catch (AppException $e) {
                            // var_dump($e->getMessage());
                            CrontabModel::where('id', $crondId)->update(['run_status' => 3]);
                        }
                    });
                }
            }
        }

        Log::get('system', 'system')->info("检查任务下发 完成");
        Log::get('system', 'system')->info('==============================================================================');
    }
}
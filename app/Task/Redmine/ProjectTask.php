<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2023/2/13 下午4:43
 * <AUTHOR>
 * @Description
 *
 */
namespace App\Task\Redmine;

use App\Constants\IssueCode;
use App\Core\Utils\Log;
use Hyperf\Crontab\Annotation\Crontab;

/**
 * redmine项目相关任务
 */
class ProjectTask
{
    /**
     *
     * @return void
     */
    public function addTrackers()
    {
        if(env('CRONTAB_ONOFF', false) === true){
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info('开始同步流程到产品');
            make(\App\Core\Services\Project\ProjectService::class)->addTrackers(IssueCode::ISSUE_TRACKER_FLOW);
            Log::get('system', 'system')->info("同步完成");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("定时任务未开启");
        }
    }

    public function syncProjectFlow()
    {
        if(env('CRONTAB_ONOFF', false) === true){
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info('开始同步流程模板中的节点到产品流程中');
            make(\App\Core\Services\Project\FlowService::class)->syncNodes();
            Log::get('system', 'system')->info("同步完成");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("定时任务未开启");
        }
    }

    public function initFactoryName()
    {
        if(env('CRONTAB_ONOFF', false) === true){
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info('bi_oa_qc表attribution字段改为拼音');
            make(\App\Core\Services\TchipOa\OaQcErpService::class)->initFactoryName();
            Log::get('system', 'system')->info("修改完成");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("定时任务未开启");
        }
    }

    public function addSoftTypeProjectModuleIssueClass()
    {
        if(env('CRONTAB_ONOFF', false) === true){
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info('所有软件项目添加启用【需求】模块');
            make(\App\Core\Services\Project\ProjectService::class)->addIssueClassModule();
            Log::get('system', 'system')->info("修改完成");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("定时任务未开启");
        }
    }

    public function addBatchBeginTimeToProjectExtTable()
    {
        if(env('CRONTAB_ONOFF', false) === true){
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info('所有软件项目添加启用【需求】模块');
            make(\App\Core\Services\Project\ProjectService::class)->addBatchBeginTimeToProjectExtTable();
            Log::get('system', 'system')->info("修改完成");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("定时任务未开启");
        }
    }

    public function productFlowNodeDeadlineNotice()
    {
        if(env('CRONTAB_ONOFF', false) === true){
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info('产品列表-所有产品逾期 & 临期(倒数2天) 节点通知对应人员');
            //make(\App\Core\Services\Project\FlowService::class)->productFlowNodeDeadlineNotice();
            make(\App\Core\Services\Project\FlowService::class)->productFlowNodeDeadlineNoticeEmail();
            Log::get('system', 'system')->info("同步完成");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("定时任务未开启");
        }
    }

}